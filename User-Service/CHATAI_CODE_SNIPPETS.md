# ChatAI Code Snippets - Exact Code to Add

This file contains the exact code snippets that need to be added to existing files.

## 📝 **1. src/app.module.ts**

### Add Import:
```typescript
import { ChatAiModule } from './chatAi/chatAi.module';
```

### Add to Module Imports Array:
```typescript
@Module({
  imports: [
    // ... existing imports
    ChatAiModule,
  ],
  // ... rest of module
})
```

## 📝 **2. src/application/entities/application.entity.ts**

### Add Import:
```typescript
import { ChatAi } from '../../chatAi/entities/chatAi.entity';
```

### Add Property to Application Class:
```typescript
@Entity('applications')
export class Application {
  // ... existing properties

  @OneToMany(() => ChatAi, (chatAi) => chatAi.app)
  chatAis: ChatAi[];
}
```

## 📝 **3. src/user/entities/user.entity.ts**

### Add Import:
```typescript
import { ChatAi } from '../../chatAi/entities/chatAi.entity';
```

### Add Property to User Class:
```typescript
@Entity('users')
export class User {
  // ... existing properties

  @OneToMany(() => ChatAi, (chatAi) => chatAi.user)
  chatAis: ChatAi[];
}
```

## 📝 **4. package.json**

### Add to Dependencies:
```json
{
  "dependencies": {
    "multer": "^1.4.5-lts.1",
    "node-fetch": "^2.6.7"
  },
  "devDependencies": {
    "@types/multer": "^1.4.7",
    "@types/node-fetch": "^2.6.4"
  }
}
```

## 📝 **5. src/CommonMessages/CommonMessages.ts**

### Add to AppMessage Class:
```typescript
export class AppMessage {
  // ... existing messages

  static ServiceCreated = (service: string) => `${service} created successfully`;
  static ServiceUpdated = (service: string) => `${service} updated successfully`;
  static ServiceDeleted = (service: string) => `${service} deleted successfully`;
  static ServiceFetched = (service: string) => `${service} fetched successfully`;
  static AppNotFound = () => 'Application not found or access denied';
  static DocumentNotFound = () => 'Document not found or access denied';
  static InsufficientCredits = () => 'Insufficient credits for this operation';
}
```

## 📝 **6. .env**

### Add Environment Variables:
```env
# ChatAI Configuration
CHATAI_SDK_URL=http://localhost:3001
INTERNAL_API_KEY=chatai-internal-2024

# File Upload Configuration
MAX_FILE_SIZE=20971520
ALLOWED_FILE_TYPES=pdf,doc,docx,txt,md

# Credit System
DEFAULT_FREE_CREDITS=100
DEFAULT_PRO_CREDITS=10000
```

## 📝 **7. src/utils/common.service.ts** (NEW FILE)

### Complete File Content:
```typescript
/**
 * Common utility functions
 */

/**
 * Calculate pagination offset
 * @param page - Page number (1-based)
 * @param limit - Items per page
 * @returns Skip offset for database query
 */
export function paginate(page: number = 1, limit: number = 10): number {
  const validPage = Math.max(1, page);
  const validLimit = Math.max(1, limit);
  return (validPage - 1) * validLimit;
}

/**
 * Generate pagination metadata
 * @param page - Current page
 * @param limit - Items per page  
 * @param totalCount - Total items
 * @returns Pagination metadata object
 */
export function getPaginationMeta(page: number, limit: number, totalCount: number) {
  const totalPages = Math.ceil(totalCount / limit);
  
  return {
    currentPage: page,
    totalPages,
    pageSize: limit,
    totalItems: totalCount,
    hasNextPage: page < totalPages,
    hasPreviousPage: page > 1,
  };
}
```

## 📊 **8. Database Migration Template**

### Create: src/migrations/XXXX-create-chat-ai-tables.ts
```typescript
import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateChatAiTables1234567890 implements MigrationInterface {
  name = 'CreateChatAiTables1234567890';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create chat_ai_projects table
    await queryRunner.query(`
      CREATE TABLE "chat_ai_projects" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying NOT NULL,
        "description" text,
        "settings" jsonb,
        "isActive" boolean NOT NULL DEFAULT true,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "userId" integer NOT NULL,
        "appId" uuid NOT NULL,
        CONSTRAINT "PK_chat_ai_projects" PRIMARY KEY ("id")
      )
    `);

    // Create chat_ai_documents table  
    await queryRunner.query(`
      CREATE TABLE "chat_ai_documents" (
        "id" SERIAL NOT NULL,
        "filename" character varying NOT NULL,
        "filesize" integer NOT NULL,
        "contentType" character varying NOT NULL,
        "status" character varying NOT NULL DEFAULT 'uploading',
        "parsedData" jsonb,
        "summary" jsonb,
        "pageCount" integer,
        "wordCount" integer,
        "errorMessage" text,
        "indexId" text,
        "userId" character varying NOT NULL,
        "projectId" uuid NOT NULL,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_chat_ai_documents" PRIMARY KEY ("id")
      )
    `);

    // Create chat_ai_messages table
    await queryRunner.query(`
      CREATE TABLE "chat_ai_messages" (
        "id" SERIAL NOT NULL,
        "question" text NOT NULL,
        "response" text NOT NULL,
        "sourceReferences" jsonb,
        "isAdminDebug" boolean NOT NULL DEFAULT false,
        "timestamp" TIMESTAMP NOT NULL DEFAULT now(),
        "chatAiId" uuid NOT NULL,
        "documentId" integer,
        CONSTRAINT "PK_chat_ai_messages" PRIMARY KEY ("id")
      )
    `);

    // Create foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "chat_ai_projects" 
      ADD CONSTRAINT "FK_chat_ai_projects_appId" 
      FOREIGN KEY ("appId") REFERENCES "applications"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "chat_ai_documents" 
      ADD CONSTRAINT "FK_chat_ai_documents_projectId" 
      FOREIGN KEY ("projectId") REFERENCES "chat_ai_projects"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "chat_ai_messages" 
      ADD CONSTRAINT "FK_chat_ai_messages_chatAiId" 
      FOREIGN KEY ("chatAiId") REFERENCES "chat_ai_projects"("id") ON DELETE CASCADE
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "chat_ai_messages"`);
    await queryRunner.query(`DROP TABLE "chat_ai_documents"`);
    await queryRunner.query(`DROP TABLE "chat_ai_projects"`);
  }
}
```

## 🔧 **9. TypeORM Configuration Update**

### Add to ormconfig.json or data source entities:
```typescript
entities: [
  // ... existing entities
  'src/chatAi/entities/*.entity.ts',
],
```

## ⚠️ **IMPORTANT NOTES**

1. **File Paths**: Ensure all import paths match your project structure
2. **Migration Timestamp**: Replace XXXX with actual timestamp
3. **Dependencies**: Run `npm install` after updating package.json
4. **Database**: Run migrations after creating migration files
5. **Testing**: Test all endpoints after integration

---

**These are all the exact code changes needed for ChatAI integration!**

# Resilient Status Update Solutions

## 🚨 **Problem: User-Service Crash During Status Update**

When ChatAI-SDK-Clean successfully processes a document but User-Service is down during status update, the document gets stuck in "processing" state in PostgreSQL while being ready in the vector database.

## 💡 **Solution 1: Retry Mechanism with Exponential Backoff**

### **Enhanced notifyUserServiceStatus Function:**

```javascript
async function notifyUserServiceStatusWithRetry(appId, documentId, status, message, additionalData = {}, maxRetries = 5) {
  const baseDelay = 1000; // 1 second
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const userServiceUrl = process.env.USER_SERVICE_URL || 'http://localhost:3000';
      const internalApiKey = process.env.INTERNAL_API_KEY || 'chatai-internal-2024';

      console.log(`📡 Attempt ${attempt}/${maxRetries}: Updating User-Service: Document ${documentId} → ${status}`);

      const updateData = {
        appId,
        documentId,
        status,
        message,
        timestamp: new Date().toISOString(),
        ...additionalData
      };

      const response = await fetch(`${userServiceUrl}/users/app/chatai/internal/update-document-status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-internal-api-key': internalApiKey
        },
        body: JSON.stringify(updateData),
        timeout: 10000 // 10 second timeout
      });

      if (response.ok) {
        const result = await response.json();
        console.log(`✅ User-Service status updated successfully: ${status}`);
        return result;
      } else {
        throw new Error(`HTTP ${response.status}: ${await response.text()}`);
      }

    } catch (error) {
      console.warn(`⚠️ Attempt ${attempt} failed: ${error.message}`);
      
      if (attempt === maxRetries) {
        console.error(`❌ All ${maxRetries} attempts failed. Storing for later retry.`);
        await storeFailedStatusUpdate(appId, documentId, status, message, additionalData);
        return null;
      }
      
      // Exponential backoff: 1s, 2s, 4s, 8s, 16s
      const delay = baseDelay * Math.pow(2, attempt - 1);
      console.log(`⏳ Waiting ${delay}ms before retry...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
}
```

## 💡 **Solution 2: Persistent Queue for Failed Updates**

### **Store Failed Updates for Later Processing:**

```javascript
const fs = require('fs').promises;
const path = require('path');

async function storeFailedStatusUpdate(appId, documentId, status, message, additionalData) {
  try {
    const failedUpdate = {
      appId,
      documentId,
      status,
      message,
      additionalData,
      timestamp: new Date().toISOString(),
      retryCount: 0
    };

    const queueFile = path.join(__dirname, '../../queue/failed_status_updates.json');
    
    // Ensure directory exists
    await fs.mkdir(path.dirname(queueFile), { recursive: true });
    
    // Read existing queue
    let queue = [];
    try {
      const existingData = await fs.readFile(queueFile, 'utf8');
      queue = JSON.parse(existingData);
    } catch (error) {
      // File doesn't exist or is empty, start with empty queue
    }
    
    // Add new failed update
    queue.push(failedUpdate);
    
    // Write back to file
    await fs.writeFile(queueFile, JSON.stringify(queue, null, 2));
    
    console.log(`💾 Stored failed status update for later retry: ${documentId} → ${status}`);
  } catch (error) {
    console.error(`❌ Failed to store failed status update: ${error.message}`);
  }
}
```

### **Background Job to Process Failed Updates:**

```javascript
async function processFailedStatusUpdates() {
  try {
    const queueFile = path.join(__dirname, '../../queue/failed_status_updates.json');
    
    // Read queue
    let queue = [];
    try {
      const data = await fs.readFile(queueFile, 'utf8');
      queue = JSON.parse(data);
    } catch (error) {
      return; // No queue file or empty
    }
    
    if (queue.length === 0) return;
    
    console.log(`🔄 Processing ${queue.length} failed status updates...`);
    
    const remainingQueue = [];
    
    for (const update of queue) {
      const success = await retryStatusUpdate(update);
      
      if (!success) {
        update.retryCount++;
        
        // Keep trying for 24 hours (max retryCount = 144 for 10-minute intervals)
        if (update.retryCount < 144) {
          remainingQueue.push(update);
        } else {
          console.error(`❌ Giving up on status update after 24 hours: ${update.documentId}`);
        }
      }
    }
    
    // Write back remaining queue
    await fs.writeFile(queueFile, JSON.stringify(remainingQueue, null, 2));
    
  } catch (error) {
    console.error(`❌ Error processing failed status updates: ${error.message}`);
  }
}

async function retryStatusUpdate(update) {
  try {
    const result = await notifyUserServiceStatus(
      update.appId,
      update.documentId,
      update.status,
      update.message,
      update.additionalData
    );
    
    if (result) {
      console.log(`✅ Successfully retried status update: ${update.documentId} → ${update.status}`);
      return true;
    }
    return false;
  } catch (error) {
    console.warn(`⚠️ Retry failed for ${update.documentId}: ${error.message}`);
    return false;
  }
}

// Run every 10 minutes
setInterval(processFailedStatusUpdates, 10 * 60 * 1000);
```

## 💡 **Solution 3: Health Check and Recovery Endpoint**

### **Add Health Check to User-Service:**

```javascript
// In ChatAI-SDK-Clean
async function checkUserServiceHealth() {
  try {
    const userServiceUrl = process.env.USER_SERVICE_URL || 'http://localhost:3000';
    const response = await fetch(`${userServiceUrl}/health`, { timeout: 5000 });
    return response.ok;
  } catch (error) {
    return false;
  }
}
```

### **Recovery Endpoint in User-Service:**

```typescript
// In User-Service chatAi.controller.ts
@Post('internal/recover-document-status')
async recoverDocumentStatus(
  @Body() data: { appId: string; documentId: string },
  @Headers() headers: any,
) {
  // Verify internal API key
  const expectedKey = process.env.INTERNAL_API_KEY || 'chatai-internal-2024';
  if (headers['x-internal-api-key'] !== expectedKey) {
    return { error: true, message: 'Invalid internal API key' };
  }

  // Check if document exists in vector database via ChatAI-SDK-Clean
  // If yes, update status to 'ready'
  // If no, update status to 'error'
  
  return await this.chatAiService.recoverDocumentStatus(data.appId, data.documentId);
}
```

## 💡 **Solution 4: Event-Driven Architecture with Message Queue**

### **Using Redis/RabbitMQ for Reliable Messaging:**

```javascript
// Publisher (ChatAI-SDK-Clean)
const redis = require('redis');
const client = redis.createClient();

async function publishStatusUpdate(appId, documentId, status, message, additionalData) {
  const statusUpdate = {
    appId,
    documentId,
    status,
    message,
    additionalData,
    timestamp: new Date().toISOString()
  };
  
  await client.lpush('status_updates', JSON.stringify(statusUpdate));
  console.log(`📤 Published status update to queue: ${documentId} → ${status}`);
}

// Consumer (User-Service)
async function processStatusUpdates() {
  while (true) {
    try {
      const result = await client.brpop('status_updates', 10); // 10 second timeout
      
      if (result) {
        const statusUpdate = JSON.parse(result[1]);
        await this.chatAiService.updateDocumentStatusInternal(statusUpdate);
        console.log(`✅ Processed status update: ${statusUpdate.documentId}`);
      }
    } catch (error) {
      console.error(`❌ Error processing status update: ${error.message}`);
      await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5s before retry
    }
  }
}
```

## 🎯 **Recommended Implementation**

**For immediate implementation:** Use **Solution 1 + Solution 2** (Retry + Persistent Queue)

**For production scale:** Consider **Solution 4** (Message Queue) for better reliability

## 📊 **Comparison of Solutions**

| Solution | Complexity | Reliability | Performance | Dependencies |
|----------|------------|-------------|-------------|--------------|
| Retry + Backoff | Low | Medium | Good | None |
| Persistent Queue | Medium | High | Good | File System |
| Health Check | Low | Medium | Good | None |
| Message Queue | High | Very High | Excellent | Redis/RabbitMQ |

## 🚀 **Quick Implementation**

The simplest fix is to enhance the existing `notifyUserServiceStatus` function with retry logic:

```javascript
// Replace current function with retry version
async function notifyUserServiceStatus(appId, documentId, status, message, additionalData = {}) {
  return await notifyUserServiceStatusWithRetry(appId, documentId, status, message, additionalData, 3);
}
```

This provides immediate resilience with minimal code changes!

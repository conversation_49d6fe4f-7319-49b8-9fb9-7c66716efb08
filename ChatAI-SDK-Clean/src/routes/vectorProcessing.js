const express = require('express');
const multer = require('multer');
const router = express.Router();
const embeddingService = require('../services/embeddingService');
const qdrantService = require('../services/qdrantService');
const llamaParseService = require('../services/llamaParseService');
const smartChunkingService = require('../services/smartChunkingService');
const databaseService = require('../services/databaseService');
const fetch = require('node-fetch');
const logger = require('../utils/logger');

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
  fileFilter: (req, file, cb) => {
    // Allow common document types
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'text/markdown',
      'text/html',
      'application/rtf'
    ];

    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`Unsupported file type: ${file.mimetype}`), false);
    }
  }
});

/**
 * Complete document upload and processing
 * Handles file upload, LlamaIndex parsing, and vector storage
 */
router.post('/upload-and-process', upload.single('file'), async (req, res) => {
  // Extract variables at the top level for error handling scope
  const { appId, documentId, userId, filename } = req.body;
  const file = req.file;

  try {
    const processingStartTime = new Date().toISOString();
    const processingStartMs = Date.now();

    console.log('\n🔧 Complete Document Upload and Processing');
    console.log('==========================================');

    // Validate required fields
    if (!appId || !documentId || !file) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: appId, documentId, file'
      });
    }

    const actualFilename = filename || file.originalname;
    console.log(`📄 File: ${actualFilename}`);
    console.log(`🏢 AppId: ${appId}`);
    console.log(`📊 File size: ${file.size} bytes`);

    // Step 1: Update status to parsing and parse document
    console.log('\n🔧 Step 1: Parsing document with LlamaIndex');
    console.log('============================================');

    await updateDocumentStatusDirect(appId, documentId, 'parsing', 'LlamaIndex parsing in progress', {
      filename: actualFilename,
      filesize: file.size,
      contentType: file.mimetype,
      processingStartedAt: new Date().toISOString()
    });

    const parseResult = await llamaParseService.parseFile(file.buffer, actualFilename);

    if (parseResult.status !== 'SUCCESS' || !parseResult.result) {
      await updateDocumentStatusDirect(appId, documentId, 'error', 'Document parsing failed', {
        errorMessage: 'LlamaIndex parsing failed',
        errorDetails: parseResult.error || 'Unknown parsing error',
        failedAt: new Date().toISOString()
      });
      throw new Error('Document parsing failed');
    }

    console.log(`✅ Document parsed successfully`);
    console.log(`📊 Text length: ${parseResult.result.text.length} characters`);
    console.log(`📄 Pages: ${parseResult.result.metadata.page_count}`);
    console.log(`📝 Words: ${parseResult.result.metadata.word_count}`);

    // Post-process to clean up tabular artifacts and formatting
    const documentInfo = {
      filename: actualFilename,
      uploadId: documentId,
      totalPages: parseResult.result.metadata.page_count,
      fileSize: file.size
    };
    const cleanedParseResult = postProcessLlamaIndexOutput(parseResult, documentInfo);

    // Step 2: Update status to processing and start vector processing
    console.log('\n🔧 Step 2: Processing document content');
    console.log('=====================================');
    await updateDocumentStatusDirect(appId, documentId, 'processing', 'Processing parsed content and creating chunks', {
      parsedData: {
        text: parseResult.result.text,
        pages: parseResult.result.pages || [],
        metadata: parseResult.result.metadata || {}
      },
      pageCount: parseResult.result.pages?.length || 0,
      wordCount: parseResult.result.text.split(/\s+/).length
    });

    // Step 3: Update status to embedding before vector generation
    console.log('\n🔧 Step 3: Starting embedding generation');
    console.log('========================================');
    await updateDocumentStatusDirect(appId, documentId, 'embedding', 'Generating embeddings for document chunks');

    // Step 4: Process for vector search
    console.log('\n🔧 Step 4: Processing for vector search');
    console.log('=======================================');

    const vectorResult = await processDocumentForVector({
      documentId,
      appId,
      filename: actualFilename,
      userId,
      parsedText: cleanedParseResult.result.text,
      pages: cleanedParseResult.result.pages, // Pass cleaned page-based data
      metadata: cleanedParseResult.result.metadata
    });

    console.log(`✅ Complete processing finished`);

    // Step 4: Update status to indexing
    console.log('\n🔧 Step 4: Storing vectors in database');
    console.log('=====================================');
    await updateDocumentStatusDirect(appId, documentId, 'indexing', 'Storing vectors in Qdrant database');

    // Step 5: Update final status to ready
    console.log('\n🔧 Step 5: Updating final status to ready');
    console.log('=========================================');

    // Create structured chunks array from parsed pages
    const parsedDataChunks = createParsedDataChunks(cleanedParseResult.result.pages || []);
    const fullText = cleanedParseResult.result.text;

    await updateDocumentStatusDirect(appId, documentId, 'ready', 'Document ready for semantic search', {
      parsedData: {
        chunks: parsedDataChunks,
        totalPages: parsedDataChunks.length,
        totalCharacters: fullText.length,
        totalWords: parseResult.result.metadata.word_count
      },
      pageCount: parseResult.result.metadata.page_count,
      wordCount: parseResult.result.metadata.word_count,
      indexId: documentId
    });

    console.log(`🎉 Document processing complete: ${documentId}`);

    // Log processing summary
    const processingEndTime = new Date().toISOString();
    const processingDurationMs = Date.now() - processingStartMs;

    const processingSummary = {
      startTime: processingStartTime,
      endTime: processingEndTime,
      durationMs: processingDurationMs,
      steps: ['parsing', 'page-filtering', 'chunk-filtering', 'embedding', 'vector-storage'],
      pages: {
        original: parseResult.result.metadata.page_count,
        filtered: cleanedParseResult.result.metadata?.page_filtering_stats?.pages_removed || 0,
        merged: cleanedParseResult.result.metadata?.page_filtering_stats?.pages_merged || 0,
        final: cleanedParseResult.result.pages.length
      },
      chunks: {
        original: vectorResult.qualityFiltering?.originalChunks || vectorResult.totalChunks,
        filtered: vectorResult.qualityFiltering?.junkChunksRemoved || 0,
        final: vectorResult.totalChunks
      },
      cost: {
        embeddingsSaved: vectorResult.qualityFiltering?.junkChunksRemoved || 0,
        estimatedSavings: `${vectorResult.qualityFiltering?.costSavingsPercent || 0}%`
      }
    };

    logger.logProcessingSummary(documentInfo, processingSummary);

    // Return comprehensive result
    res.json({
      success: true,
      message: 'Document uploaded, parsed, and processed for vector search',
      data: {
        documentId,
        appId,
        filename: actualFilename,
        parsing: {
          jobId: parseResult.id,
          textLength: parseResult.result.text.length,
          pageCount: parseResult.result.metadata.page_count,
          wordCount: parseResult.result.metadata.word_count
        },
        vectorProcessing: {
          totalChunks: vectorResult.totalChunks,
          storedChunks: vectorResult.storedChunks,
          chunkingStrategy: vectorResult.chunkingStrategy,
          pageBasedChunks: vectorResult.pageBasedChunks,
          status: 'ready_for_chat'
        },
        status: 'ready_for_chat'
      }
    });

  } catch (error) {
    console.error('❌ Complete document processing error:', error);

    // Update status to error
    if (appId && documentId) {
      await updateDocumentStatusDirect(appId, documentId, 'error', `Processing failed: ${error.message}`, {
        errorMessage: error.message,
        errorStack: error.stack,
        failedAt: new Date().toISOString()
      });
    }

    res.status(500).json({
      success: false,
      error: 'Complete document processing failed',
      message: error.message
    });
  }
});

/**
 * Process document for vector search
 * Called by User-Service after LlamaIndex parsing is complete
 */
router.post('/process-document', async (req, res) => {
  try {
    console.log('\n🔧 Processing document for vector search');
    console.log('========================================');

    const {
      documentId,
      appId,
      filename,
      userId,
      parsedText,
      metadata = {}
    } = req.body;

    // Validate required fields
    if (!documentId || !appId || !parsedText) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: documentId, appId, parsedText'
      });
    }

    const result = await processDocumentForVector({
      documentId,
      appId,
      filename,
      userId,
      parsedText,
      metadata
    });

    // Return success response
    res.json({
      success: true,
      message: 'Document processed for vector search',
      data: {
        documentId,
        appId,
        totalChunks: result.totalChunks,
        storedChunks: result.storedChunks,
        chunks: result.chunks,
        status: 'ready_for_chat'
      }
    });

  } catch (error) {
    console.error('❌ Vector processing error:', error);
    res.status(500).json({
      success: false,
      error: 'Vector processing failed',
      message: error.message
    });
  }
});

/**
 * Attempt smart merging of a short page with adjacent pages
 * @param {Array} pages - All pages array
 * @param {number} currentIndex - Index of current short page
 * @param {Object} criteria - Filtering criteria
 * @param {Object} report - Filter report for tracking
 * @returns {Object} Merge result with success flag and merged page
 */
function attemptSmartMerge(pages, currentIndex, criteria, report) {
  const currentPage = pages[currentIndex];
  const currentText = currentPage.text || currentPage.markdown || '';
  const currentWords = countWords(currentText);
  const currentPageNumber = currentPage.pageNumber || currentIndex + 1;

  // Strategy 1: Merge with next page (forward merge)
  if (currentIndex + 1 < pages.length) {
    const nextPage = pages[currentIndex + 1];
    const nextText = nextPage.text || nextPage.markdown || '';
    const nextWords = countWords(nextText);

    // Check if merging with next page creates good content
    const forwardMergeResult = evaluateMerge(currentPage, nextPage, 'forward', criteria);
    if (forwardMergeResult.viable) {
      return {
        success: true,
        mergedPage: forwardMergeResult.mergedPage,
        mergeInfo: {
          strategy: 'forward',
          resultPageNumber: currentPageNumber,
          mergedPages: [currentPageNumber, nextPage.pageNumber || currentIndex + 2],
          originalWords: currentWords + nextWords,
          mergedWords: forwardMergeResult.mergedPage.wordCount,
          contextType: forwardMergeResult.contextType
        },
        pagesConsumed: 2
      };
    }
  }

  // Strategy 2: Merge with previous page (backward merge)
  if (currentIndex > 0 && report.filtered.length === 0) { // Only if we haven't started filtering yet
    const prevPage = pages[currentIndex - 1];
    const prevText = prevPage.text || prevPage.markdown || '';
    const prevWords = countWords(prevText);

    // Check if merging with previous page creates good content
    const backwardMergeResult = evaluateMerge(prevPage, currentPage, 'backward', criteria);
    if (backwardMergeResult.viable) {
      return {
        success: true,
        mergedPage: backwardMergeResult.mergedPage,
        mergeInfo: {
          strategy: 'backward',
          resultPageNumber: prevPage.pageNumber || currentIndex,
          mergedPages: [prevPage.pageNumber || currentIndex, currentPageNumber],
          originalWords: prevWords + currentWords,
          mergedWords: backwardMergeResult.mergedPage.wordCount,
          contextType: backwardMergeResult.contextType
        },
        pagesConsumed: 1 // Only consume current page, previous already processed
      };
    }
  }

  // Strategy 3: Multi-page merge (combine with multiple adjacent short pages)
  if (currentIndex + 2 < pages.length) {
    const multiMergeResult = evaluateMultiPageMerge(pages, currentIndex, criteria);
    if (multiMergeResult.viable) {
      return {
        success: true,
        mergedPage: multiMergeResult.mergedPage,
        mergeInfo: {
          strategy: 'multi-page',
          resultPageNumber: currentPageNumber,
          mergedPages: multiMergeResult.pageNumbers,
          originalWords: multiMergeResult.originalWords,
          mergedWords: multiMergeResult.mergedPage.wordCount,
          contextType: multiMergeResult.contextType
        },
        pagesConsumed: multiMergeResult.pagesConsumed
      };
    }
  }

  return { success: false };
}

/**
 * Evaluate if two pages should be merged and create merged page
 * @param {Object} firstPage - First page to merge
 * @param {Object} secondPage - Second page to merge
 * @param {string} direction - 'forward' or 'backward'
 * @param {Object} criteria - Filtering criteria
 * @returns {Object} Merge evaluation result
 */
function evaluateMerge(firstPage, secondPage, direction, criteria) {
  const firstText = firstPage.text || firstPage.markdown || '';
  const secondText = secondPage.text || secondPage.markdown || '';
  const firstWords = countWords(firstText);
  const secondWords = countWords(secondText);

  // Determine context relationship
  const contextType = determineContextType(firstText, secondText, direction);

  // Create merged text with appropriate formatting
  const mergedText = createMergedText(firstText, secondText, contextType);
  const mergedWords = countWords(mergedText);

  // Check if merge meets quality criteria
  const viable = mergedWords >= criteria.minWords &&
    mergedWords <= criteria.minWords * 4 && // Don't create overly long pages
    contextType !== 'incompatible';

  if (!viable) {
    return { viable: false };
  }

  // Create merged page with enhanced metadata
  const mergedPage = {
    ...firstPage,
    text: mergedText,
    markdown: mergedText,
    wordCount: mergedWords,
    pageNumber: firstPage.pageNumber,
    mergedFrom: [firstPage.pageNumber, secondPage.pageNumber],
    isMerged: true,
    mergeStrategy: direction,
    contextType: contextType,
    originalPages: [
      {
        pageNumber: firstPage.pageNumber,
        wordCount: firstWords,
        preview: firstText.substring(0, 50) + '...'
      },
      {
        pageNumber: secondPage.pageNumber,
        wordCount: secondWords,
        preview: secondText.substring(0, 50) + '...'
      }
    ]
  };

  return {
    viable: true,
    mergedPage,
    contextType
  };
}

/**
 * Evaluate multi-page merge for several consecutive short pages
 * @param {Array} pages - All pages
 * @param {number} startIndex - Starting index
 * @param {Object} criteria - Filtering criteria
 * @returns {Object} Multi-merge evaluation result
 */
function evaluateMultiPageMerge(pages, startIndex, criteria) {
  const pagesToMerge = [pages[startIndex]];
  const pageNumbers = [pages[startIndex].pageNumber || startIndex + 1];
  let totalWords = countWords(pages[startIndex].text || '');
  let currentIndex = startIndex + 1;

  // Collect consecutive short pages
  while (currentIndex < pages.length && pagesToMerge.length < 4) { // Max 4 pages
    const page = pages[currentIndex];
    const pageText = page.text || page.markdown || '';
    const pageWords = countWords(pageText);

    // Only include if it's also short
    if (pageWords < criteria.minWords) {
      pagesToMerge.push(page);
      pageNumbers.push(page.pageNumber || currentIndex + 1);
      totalWords += pageWords;
      currentIndex++;
    } else {
      break;
    }
  }

  // Only proceed if we have multiple pages and reasonable total length
  if (pagesToMerge.length < 2 || totalWords > criteria.minWords * 3) {
    return { viable: false };
  }

  // Create merged text
  const mergedTexts = pagesToMerge.map(page => page.text || page.markdown || '');
  const mergedText = mergedTexts.join('\n\n').trim();
  const mergedWords = countWords(mergedText);

  const mergedPage = {
    ...pagesToMerge[0],
    text: mergedText,
    markdown: mergedText,
    wordCount: mergedWords,
    pageNumber: pagesToMerge[0].pageNumber,
    mergedFrom: pageNumbers,
    isMerged: true,
    mergeStrategy: 'multi-page',
    contextType: 'sequential',
    originalPages: pagesToMerge.map((page, index) => ({
      pageNumber: pageNumbers[index],
      wordCount: countWords(page.text || ''),
      preview: (page.text || '').substring(0, 50) + '...'
    }))
  };

  return {
    viable: mergedWords >= criteria.minWords,
    mergedPage,
    pageNumbers,
    originalWords: totalWords,
    pagesConsumed: pagesToMerge.length,
    contextType: 'sequential'
  };
}

/**
 * Determine the context relationship between two text blocks
 * @param {string} firstText - First text block
 * @param {string} secondText - Second text block
 * @param {string} direction - Merge direction
 * @returns {string} Context type
 */
function determineContextType(firstText, secondText, direction) {
  const first = firstText.trim().toLowerCase();
  const second = secondText.trim().toLowerCase();

  // Header-content relationship
  if (isHeaderText(first) && isContentText(second)) {
    return 'header-content';
  }

  // Content-footer relationship
  if (isContentText(first) && isFooterText(second)) {
    return 'content-footer';
  }

  // Sequential content (both are content)
  if (isContentText(first) && isContentText(second)) {
    return 'sequential-content';
  }

  // Section continuation
  if (isSectionContinuation(first, second)) {
    return 'section-continuation';
  }

  // Default to sequential if no specific relationship
  return 'sequential';
}

/**
 * Create merged text with appropriate formatting based on context
 * @param {string} firstText - First text
 * @param {string} secondText - Second text
 * @param {string} contextType - Type of context relationship
 * @returns {string} Properly formatted merged text
 */
function createMergedText(firstText, secondText, contextType) {
  const first = firstText.trim();
  const second = secondText.trim();

  switch (contextType) {
    case 'header-content':
      // Header followed by content - single line break
      return `${first}\n\n${second}`;

    case 'content-footer':
      // Content followed by footer - might want to exclude footer
      return first; // Often better to exclude footers

    case 'section-continuation':
      // Continuing section - single line break
      return `${first}\n\n${second}`;

    case 'sequential-content':
      // Sequential content - double line break for paragraph separation
      return `${first}\n\n${second}`;

    default:
      // Default formatting
      return `${first}\n\n${second}`;
  }
}

/**
 * Check if text appears to be a header
 */
function isHeaderText(text) {
  const headerPatterns = [
    /^(chapter|section|part)\s+\d+/i,
    /^(appendix\s+[a-z])/i,
    /^\d+\.\s*[a-z]/i, // Numbered sections
  ];

  // Check regex patterns
  const matchesPattern = headerPatterns.some(pattern => pattern.test(text));

  // Check if it's short title-like text
  const isShortTitle = /^[a-z\s]+$/i.test(text) && text.length < 50;

  return matchesPattern || isShortTitle ||
    (text.length < 50 && !text.includes('.') && !text.includes(','));
}

/**
 * Check if text appears to be content
 */
function isContentText(text) {
  return text.length > 50 &&
    (text.includes('.') || text.includes(',')) &&
    countWords(text) > 10;
}

/**
 * Check if text appears to be a footer
 */
function isFooterText(text) {
  const footerPatterns = [
    /^page\s+\d+/i,
    /^©.*rights/i,
    /^copyright/i
  ];

  return footerPatterns.some(pattern => pattern.test(text));
}

/**
 * Check if second text continues the first text's section
 */
function isSectionContinuation(first, second) {
  // Simple heuristic: if first ends mid-sentence and second continues
  return first.length > 0 &&
    !first.endsWith('.') &&
    !first.endsWith('!') &&
    !first.endsWith('?') &&
    second.length > 0 &&
    !second.match(/^(chapter|section|part)/i);
}

/**
 * Detect if short content contains valuable information that should be preserved
 * @param {string} content - Page content to analyze
 * @param {Object} criteria - Filtering criteria with valuable patterns
 * @returns {string|null} Content type if valuable, null otherwise
 */
function detectValuableShortContent(content, criteria) {
  const lowerContent = content.toLowerCase().trim();

  // Check for mathematical content first (most specific)
  if (/\$[^$]+\$/.test(content) || /\\[a-z]+\{/.test(content)) {
    return 'mathematical';
  }

  // Check for definitions (specific patterns)
  if (/^(definition|formula|equation)\s*:?/i.test(content)) {
    return 'definition';
  }

  // Check for key points and summaries (specific patterns)
  if (/^(key\s+points?|summary|conclusion|takeaways?|important|note)\s*:?/i.test(content)) {
    return 'keyPoint';
  }

  // Check for citations (specific patterns)
  if (/^(quote|citation)\s*:?/i.test(content) || /\[[0-9]+\]/.test(content)) {
    return 'citation';
  }

  // Check for academic/research section headers (prioritize over technical topics)
  if (/^(analysis|discussion|methodology|results|introduction|background|related\s+work|experiment|study|research|investigation|conclusion|future\s+work|limitations)/i.test(content)) {
    return 'sectionHeader';
  }

  // Check for AI safety and ethics topics (prioritize safety over technical)
  if (/(misinformation|disinformation|bias|fairness|harm|safety|ethics|responsible\s+ai|toxicity|hate\s+speech|discrimination|privacy|security|robustness|alignment|interpretability|explainability)/i.test(content)) {
    return 'safetyTopic';
  }

  // Check for technical ML/AI topics (broader patterns)
  if (/(pretraining|pre-training|finetuning|fine-tuning|training|evaluation|evaluating|embedding|embeddings|transformer|attention|neural\s+network|deep\s+learning|language\s+model|llm|gpt|bert|algorithm|dataset|data\s+processing|performance|accuracy|metrics|benchmark)/i.test(content)) {
    return 'technicalTopic';
  }

  // Check for structured content (lists, numbered items)
  if (/^\d+\.\s+.+/m.test(content) || /^[•\-\*]\s+.+/m.test(content)) {
    return 'keyPoint';
  }

  return null;
}

/**
 * Filter and merge short pages from LlamaIndex output
 * @param {Array} pages - Array of page objects from LlamaIndex
 * @param {Object} documentInfo - Document information for logging
 * @returns {Object} Filtered pages and filter report
 */
function filterAndMergeShortPages(pages, documentInfo = {}) {
  const pageFilterCriteria = {
    minWords: 20,           // Minimum words per page
    minChars: 150,          // Minimum characters per page
    minMeaningfulRatio: 0.4, // Minimum ratio of meaningful content
    headerOnlyPatterns: [
      /^(chapter|section|part)\s+\d+\s*$/i,
      /^(appendix\s+[a-z])\s*$/i,
      /^(table\s+of\s+contents?)\s*$/i,
      /^(index|bibliography|references)\s*$/i,
    ],
    footerOnlyPatterns: [
      /^(page\s+\d+(\s+of\s+\d+)?)\s*$/i,
      /^©.*rights?\s+reserved/i,
      /^copyright\s+\d{4}/i,
    ],
    // Valuable short content patterns that should be preserved
    valuableShortPatterns: [
      /^(key\s+points?|summary|conclusion|takeaways?)\s*:?/i,
      /^(important|note|warning|caution)\s*:?/i,
      /^(definition|formula|equation)\s*:?/i,
      /^(result|finding|outcome)\s*:?/i,
      /^(quote|citation)\s*:?/i,
      /\$[^$]+\$/,  // Mathematical formulas
      /^\d+\.\s+.+/,  // Numbered lists
      /^[•\-\*]\s+.+/,  // Bullet points

      // Technical ML/AI terms that indicate valuable content
      /(pretraining|pre-training|finetuning|fine-tuning|training)/i,
      /(evaluation|evaluating|assessment|testing|validation)/i,
      /(embedding|embeddings|vector|representation)/i,
      /(transformer|attention|neural\s+network|deep\s+learning)/i,
      /(language\s+model|llm|gpt|bert|model)/i,
      /(algorithm|methodology|approach|technique)/i,
      /(dataset|data\s+processing|corpus)/i,
      /(performance|accuracy|metrics|benchmark)/i,

      // AI safety and ethics terms
      /(misinformation|disinformation|bias|fairness)/i,
      /(harm|safety|ethics|responsible\s+ai)/i,
      /(toxicity|hate\s+speech|discrimination)/i,
      /(privacy|security|robustness)/i,
      /(alignment|interpretability|explainability)/i,

      // Academic/research terms
      /(analysis|discussion|methodology|results)/i,
      /(introduction|background|related\s+work)/i,
      /(experiment|study|research|investigation)/i,
      /(conclusion|future\s+work|limitations)/i,
    ],
    // Content types that should have lower thresholds
    specialContentTypes: {
      mathematical: { minWords: 5, minChars: 30 },
      definition: { minWords: 8, minChars: 50 },
      keyPoint: { minWords: 10, minChars: 80 },
      citation: { minWords: 3, minChars: 20 },
      technicalTopic: { minWords: 3, minChars: 15 },
      safetyTopic: { minWords: 3, minChars: 15 },
      sectionHeader: { minWords: 3, minChars: 20 }
    }
  };

  const filteredPages = [];
  const pageFilterReport = {
    filtered: [],
    merged: [],
    reasons: {
      tooShort: 0,
      headerOnly: 0,
      footerOnly: 0,
      lowMeaningful: 0,
      merged: 0
    }
  };

  let i = 0;
  while (i < pages.length) {
    const page = pages[i];
    const pageText = page.text || page.markdown || '';
    const wordCount = countWords(pageText);
    const charCount = pageText.length;
    const pageNumber = page.pageNumber || i + 1;

    // Calculate meaningful content ratio
    const meaningfulContent = pageText.replace(/[\s\n\r\t]+/g, ' ').trim();
    const meaningfulChars = (meaningfulContent.match(/[a-zA-Z0-9]/g) || []).length;
    const meaningfulRatio = meaningfulContent.length > 0 ? meaningfulChars / meaningfulContent.length : 0;

    let shouldFilter = false;
    let filterReason = '';

    // Check if page is too short, but first check if it contains valuable content
    if (wordCount < pageFilterCriteria.minWords) {
      const valuableContentType = detectValuableShortContent(meaningfulContent, pageFilterCriteria);

      if (valuableContentType) {
        // Apply special thresholds for valuable content
        const specialCriteria = pageFilterCriteria.specialContentTypes[valuableContentType];
        if (wordCount >= specialCriteria.minWords && charCount >= specialCriteria.minChars) {
          console.log(`📌 Preserving valuable short content (${valuableContentType}): Page ${pageNumber} - "${meaningfulContent.substring(0, 50)}..."`);
          // Don't filter - this is valuable short content
        } else {
          shouldFilter = true;
          filterReason = `Too short (${wordCount} < ${pageFilterCriteria.minWords} words) - ${valuableContentType} content below threshold`;
          pageFilterReport.reasons.tooShort++;
        }
      } else {
        shouldFilter = true;
        filterReason = `Too short (${wordCount} < ${pageFilterCriteria.minWords} words)`;
        pageFilterReport.reasons.tooShort++;
      }
    }

    // Check if page is header-only
    if (!shouldFilter) {
      for (const pattern of pageFilterCriteria.headerOnlyPatterns) {
        if (pattern.test(meaningfulContent)) {
          shouldFilter = true;
          filterReason = 'Header-only content';
          pageFilterReport.reasons.headerOnly++;
          break;
        }
      }
    }

    // Check if page is footer-only
    if (!shouldFilter) {
      for (const pattern of pageFilterCriteria.footerOnlyPatterns) {
        if (pattern.test(meaningfulContent)) {
          shouldFilter = true;
          filterReason = 'Footer-only content';
          pageFilterReport.reasons.footerOnly++;
          break;
        }
      }
    }

    // Check meaningful content ratio
    if (!shouldFilter && meaningfulRatio < pageFilterCriteria.minMeaningfulRatio) {
      shouldFilter = true;
      filterReason = `Low meaningful content (${Math.round(meaningfulRatio * 100)}% < ${Math.round(pageFilterCriteria.minMeaningfulRatio * 100)}%)`;
      pageFilterReport.reasons.lowMeaningful++;
    }

    if (shouldFilter) {
      // Smart merging strategy: try multiple approaches to preserve context
      const mergeResult = attemptSmartMerge(pages, i, pageFilterCriteria, pageFilterReport);

      if (mergeResult.success) {
        // Successfully merged - add the merged page and skip processed pages
        filteredPages.push(mergeResult.mergedPage);
        pageFilterReport.merged.push(mergeResult.mergeInfo);
        pageFilterReport.reasons.merged++;

        // Log the merge operation
        logger.logPageFiltering(
          documentInfo,
          {
            pageNumber: pageNumber,
            content: pageText,
            wordCount: wordCount,
            charCount: charCount,
            meaningfulRatio: meaningfulRatio,
            filterReason: filterReason
          },
          'MERGED',
          mergeResult.mergeInfo
        );

        // Skip all pages that were merged
        i += mergeResult.pagesConsumed;
        continue;
      }

      // Could not merge - check if content is valuable before filtering
      const valuableContentType = detectValuableShortContent(meaningfulContent, pageFilterCriteria);

      if (valuableContentType) {
        // This is valuable content that couldn't be merged - preserve it anyway
        console.log(`📌 Preserving valuable short content that couldn't be merged: Page ${pageNumber} (${valuableContentType})`);
        console.log(`   📝 Content: "${meaningfulContent.substring(0, 60)}..."`);

        // Add special metadata to indicate this is preserved valuable short content
        const preservedPage = {
          ...page,
          preservedShortContent: true,
          contentType: valuableContentType,
          preservationReason: 'Valuable content below size threshold'
        };

        filteredPages.push(preservedPage);
        pageFilterReport.preserved = pageFilterReport.preserved || [];
        pageFilterReport.preserved.push({
          pageNumber,
          contentType: valuableContentType,
          reason: 'Valuable short content preserved',
          chars: charCount,
          words: wordCount
        });

        // Log the preservation
        logger.logPageFiltering(
          documentInfo,
          {
            pageNumber: pageNumber,
            content: pageText,
            wordCount: wordCount,
            charCount: charCount,
            meaningfulRatio: meaningfulRatio,
            filterReason: `Preserved valuable ${valuableContentType} content`
          },
          'PRESERVED'
        );
      } else {
        // Filter out this page - no valuable content detected
        pageFilterReport.filtered.push({
          pageNumber,
          reason: filterReason,
          chars: charCount,
          words: wordCount,
          content: pageText
        });

        // Log the filtered page
        logger.logPageFiltering(
          documentInfo,
          {
            pageNumber: pageNumber,
            content: pageText,
            wordCount: wordCount,
            charCount: charCount,
            meaningfulRatio: meaningfulRatio,
            filterReason: filterReason
          },
          'FILTERED'
        );
      }

      i++;
      continue;
    }

    // Page passed all filters - keep it
    filteredPages.push(page);
    i++;
  }

  return {
    filteredPages,
    pageFilterReport
  };
}

/**
 * Post-process LlamaIndex output to clean up tabular artifacts and formatting
 * @param {Object} parseResult - LlamaIndex parse result
 * @param {Object} documentInfo - Document information for logging
 * @returns {Object} Cleaned parse result
 */
function postProcessLlamaIndexOutput(parseResult, documentInfo = {}) {
  if (!parseResult.result || !parseResult.result.pages) {
    return parseResult;
  }

  console.log('🧹 Post-processing LlamaIndex output: filtering short pages and cleaning artifacts...');

  // Step 1: Filter and merge short pages
  const { filteredPages, pageFilterReport } = filterAndMergeShortPages(parseResult.result.pages, documentInfo);

  console.log(`📊 Page filtering results:`);
  console.log(`   📄 Original pages: ${parseResult.result.pages.length}`);
  console.log(`   ✅ Quality pages: ${filteredPages.length}`);
  console.log(`   🗑️  Filtered/merged: ${pageFilterReport.filtered.length}`);
  console.log(`   💰 Page-level savings: ${Math.round((pageFilterReport.filtered.length / parseResult.result.pages.length) * 100)}% fewer pages to process`);

  if (pageFilterReport.filtered.length > 0) {
    console.log(`\n🗑️  Filtered pages details:`);
    pageFilterReport.filtered.forEach((item, index) => {
      const preview = item.content ? item.content.substring(0, 80).replace(/\n/g, ' ').trim() + '...' : 'N/A';
      console.log(`   📄 Page ${item.pageNumber}: ${item.reason}`);
      console.log(`      📊 Stats: ${item.chars} chars, ${item.words} words`);
      console.log(`      📝 Content: "${preview}"`);
      if (index < pageFilterReport.filtered.length - 1) console.log('');
    });
  }

  if (pageFilterReport.merged.length > 0) {
    console.log(`\n🔗 Merged pages details:`);
    pageFilterReport.merged.forEach(item => {
      console.log(`   📄 Page ${item.mergedPages.join(' + ')}: ${item.strategy} merge (${item.originalWords} → ${item.mergedWords} words)`);
    });
  }

  // Step 2: Clean tabular artifacts from remaining pages
  const cleanedPages = filteredPages.map((page, index) => {
    const originalText = page.text || '';
    const originalMarkdown = page.markdown || '';

    // Clean both text and markdown
    const cleanedText = cleanTabularContent(originalText);
    const cleanedMarkdown = cleanTabularContent(originalMarkdown);

    const originalWordCount = page.wordCount || 0;
    const cleanedWordCount = countWords(cleanedText);
    const reductionPercent = originalWordCount > 0 ? Math.round(((originalWordCount - cleanedWordCount) / originalWordCount) * 100) : 0;

    if (reductionPercent > 10) {
      console.log(`   📄 Page ${page.pageNumber || index + 1}: Cleaned ${reductionPercent}% tabular artifacts (${originalWordCount} → ${cleanedWordCount} words)`);
    }

    return {
      ...page,
      text: cleanedText,
      markdown: cleanedMarkdown,
      wordCount: cleanedWordCount,
      cleaningStats: {
        originalWordCount,
        cleanedWordCount,
        reductionPercent
      }
    };
  });

  // Update full text
  const cleanedFullText = cleanedPages
    .map(page => page.text || '')
    .join('\n\n');

  const totalOriginalWords = parseResult.result.metadata?.word_count || 0;
  const totalCleanedWords = countWords(cleanedFullText);
  const totalReduction = totalOriginalWords > 0 ? Math.round(((totalOriginalWords - totalCleanedWords) / totalOriginalWords) * 100) : 0;

  console.log(`✅ Post-processing complete:`);
  console.log(`   📄 Page filtering: ${pageFilterReport.filtered.length} pages removed, ${pageFilterReport.merged.length} merges`);
  console.log(`   🧹 Artifact cleaning: ${totalReduction}% tabular artifacts removed (${totalOriginalWords} → ${totalCleanedWords} words)`);

  // Show detailed filtering summary
  const totalPagesProcessed = parseResult.result.pages.length;
  const pagesKept = cleanedPages.length;
  const pageReductionPercent = Math.round((pageFilterReport.filtered.length / totalPagesProcessed) * 100);

  console.log(`\n📊 Page Processing Summary:`);
  console.log(`   📄 Original pages: ${totalPagesProcessed}`);
  console.log(`   ✅ Pages kept: ${pagesKept}`);
  console.log(`   🗑️  Pages filtered: ${pageFilterReport.filtered.length} (${pageReductionPercent}%)`);
  console.log(`   🔗 Pages merged: ${pageFilterReport.merged.length}`);
  console.log(`   💰 Processing efficiency: ${Math.round((pagesKept / totalPagesProcessed) * 100)}% content retained`);

  return {
    ...parseResult,
    result: {
      ...parseResult.result,
      text: cleanedFullText,
      pages: cleanedPages,
      metadata: {
        ...parseResult.result.metadata,
        word_count: totalCleanedWords,
        page_filtering_stats: {
          original_pages: parseResult.result.pages.length,
          filtered_pages: cleanedPages.length,
          pages_removed: pageFilterReport.filtered.length,
          pages_merged: pageFilterReport.merged.length,
          page_reduction_percent: Math.round((pageFilterReport.filtered.length / parseResult.result.pages.length) * 100)
        },
        cleaning_stats: {
          original_word_count: totalOriginalWords,
          cleaned_word_count: totalCleanedWords,
          reduction_percent: totalReduction
        }
      }
    }
  };
}

/**
 * Clean tabular content and formatting artifacts from text
 * @param {string} text - Text to clean
 * @returns {string} Cleaned text
 */
function cleanTabularContent(text) {
  if (!text) return '';

  const lines = text.split('\n');
  const cleanedLines = [];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const trimmedLine = line.trim();

    // Skip empty lines
    if (!trimmedLine) {
      // Keep some empty lines for paragraph separation, but not excessive ones
      if (cleanedLines.length > 0 && cleanedLines[cleanedLines.length - 1] !== '') {
        cleanedLines.push('');
      }
      continue;
    }

    // Skip table formatting artifacts
    if (isTableArtifact(trimmedLine)) {
      continue;
    }

    // Skip navigation elements
    if (isNavigationElement(trimmedLine)) {
      continue;
    }

    // Skip copyright notices
    if (isCopyrightNotice(trimmedLine)) {
      continue;
    }

    // Clean up the line and add it
    const cleanedLine = cleanLine(line);
    if (cleanedLine.trim().length > 0) {
      cleanedLines.push(cleanedLine);
    }
  }

  // Join lines and clean up excessive whitespace
  return cleanedLines
    .join('\n')
    .replace(/\n{3,}/g, '\n\n')  // Max 2 consecutive newlines
    .trim();
}

/**
 * Check if a line is a table formatting artifact
 */
function isTableArtifact(line) {
  const tablePatterns = [
    /^[-=_\s|+]*$/,                    // Lines of dashes, equals, underscores, pipes
    /^[-=_]{3,}.*[-=_]{3,}$/,          // Surrounded by dashes/equals
    /^\s*\|\s*[-=_\s]*\|\s*$/,        // Table separator rows
    /^\s*[+\-|=\s]{10,}\s*$/,         // Complex table borders
    /^[-=_\s]*[A-Za-z\s]+[-=_\s]*$/,  // Text surrounded by formatting chars
  ];

  return tablePatterns.some(pattern => pattern.test(line));
}

/**
 * Check if a line is a navigation element
 */
function isNavigationElement(line) {
  const navPatterns = [
    /^(page\s+\d+(\s+of\s+\d+)?)\s*$/i,
    /^(chapter|section)\s+\d+\s*$/i,
    /^(table\s+of\s+contents?)\s*$/i,
    /^(index)\s*$/i,
    /^(appendix\s+[a-z])\s*$/i,
  ];

  return navPatterns.some(pattern => pattern.test(line));
}

/**
 * Check if a line is a copyright notice
 */
function isCopyrightNotice(line) {
  const copyrightPatterns = [
    /^©.*rights?\s+reserved/i,
    /^copyright\s+\d{4}/i,
    /^all\s+rights?\s+reserved/i,
  ];

  return copyrightPatterns.some(pattern => pattern.test(line));
}

/**
 * Clean up a line by removing excessive formatting
 */
function cleanLine(line) {
  return line
    .replace(/\s+/g, ' ')           // Normalize whitespace
    .replace(/[^\S\n]{2,}/g, ' ')   // Remove excessive spaces
    .trim();
}

/**
 * Count words in text
 */
function countWords(text) {
  if (!text) return 0;
  return text.trim().split(/\s+/).filter(word => word.length > 0).length;
}

/**
 * Create structured chunks array from parsed pages for API response
 * @param {Array} pages - Array of page objects from LlamaIndex
 * @returns {Array} Array of structured chunks with page and text information
 */
function createParsedDataChunks(pages) {
  if (!pages || !Array.isArray(pages)) {
    return [];
  }

  return pages.map((page, index) => {
    const pageText = page.text || page.markdown || '';
    const pageNumber = page.pageNumber || index + 1;
    const wordCount = page.wordCount || countWords(pageText);

    // Create a preview if the page text is very long (over 2000 characters)
    const maxChunkLength = 2000;
    const isLongPage = pageText.length > maxChunkLength;
    const displayText = isLongPage
      ? pageText.substring(0, maxChunkLength) + '...'
      : pageText;

    return {
      page: pageNumber,
      text: displayText,
      wordCount: wordCount,
      characterCount: pageText.length,
      isPreview: isLongPage,
      hasImages: (page.images && page.images.length > 0),
      imageCount: page.images ? page.images.length : 0,
      isMerged: page.isMerged || false,
      mergedFrom: page.mergedFrom || null
    };
  }).filter(chunk => chunk.text.trim().length > 0); // Filter out empty chunks
}

/**
 * Create full chunks array (without preview truncation) for complete content access
 * @param {Array} pages - Array of page objects from LlamaIndex
 * @returns {Array} Array of complete chunks with full text content
 */
function createFullDataChunks(pages) {
  if (!pages || !Array.isArray(pages)) {
    return [];
  }

  return pages.map((page, index) => {
    const pageText = page.text || page.markdown || '';
    const pageNumber = page.pageNumber || index + 1;
    const wordCount = page.wordCount || countWords(pageText);

    return {
      page: pageNumber,
      text: pageText, // Full text without truncation
      wordCount: wordCount,
      characterCount: pageText.length,
      hasImages: (page.images && page.images.length > 0),
      imageCount: page.images ? page.images.length : 0,
      isMerged: page.isMerged || false,
      mergedFrom: page.mergedFrom || null,
      originalPages: page.originalPages || null
    };
  }).filter(chunk => chunk.text.trim().length > 0); // Filter out empty chunks
}

/**
 * Filter out junk chunks that have low semantic value, including tabular artifacts
 * @param {string[]} chunks - Array of text chunks
 * @param {Object[]} chunkMetadata - Array of chunk metadata
 * @param {Object} documentInfo - Document information for logging
 * @returns {Object} Filtered chunks, metadata, and filter report
 */
function filterJunkChunks(chunks, chunkMetadata, documentInfo = {}) {
  // Configurable filtering criteria
  const filterCriteria = {
    minChars: 100,        // Minimum character count
    minWords: 15,         // Minimum word count
    minMeaningfulRatio: 0.3, // Minimum ratio of meaningful content
    maxRepetitionRatio: 0.7, // Maximum ratio of repeated content
    // Tabular and formatting artifact patterns
    tableArtifactPatterns: [
      /^[-=_\s|+]*$/,                    // Lines of dashes, equals, underscores, pipes
      /^[-=_]{3,}.*[-=_]{3,}$/,          // Surrounded by dashes/equals (table borders)
      /^\s*\|\s*[-=_\s]*\|\s*$/,        // Table separator rows
      /^\s*[+\-|=\s]{10,}\s*$/,         // Complex table borders
      /^[-=_\s]*[A-Za-z\s]+[-=_\s]*$/,  // Text surrounded by formatting chars
    ],
    navigationPatterns: [
      /^(page\s+\d+(\s+of\s+\d+)?)\s*$/i,     // Page numbers
      /^(chapter|section)\s+\d+\s*$/i,         // Chapter/section headers
      /^(table\s+of\s+contents?)\s*$/i,        // TOC headers
      /^(index)\s*$/i,                         // Index headers
      /^(appendix\s+[a-z])\s*$/i,             // Appendix headers
    ],
    copyrightPatterns: [
      /^©.*rights?\s+reserved/i,               // Copyright notices
      /^copyright\s+\d{4}/i,                   // Copyright year
      /^all\s+rights?\s+reserved/i,            // Rights reserved
    ]
  };

  const filteredChunks = [];
  const filteredMetadata = [];
  const filterReport = {
    filtered: [],
    reasons: {
      tooShort: 0,
      lowWordCount: 0,
      lowMeaningful: 0,
      highRepetition: 0,
      emptyContent: 0
    }
  };

  chunks.forEach((chunk, index) => {
    const metadata = chunkMetadata[index];
    const chunkId = metadata.pageNumber ? `Page ${metadata.pageNumber}` : `Chunk ${index + 1}`;

    // Calculate chunk statistics
    const charCount = chunk.length;
    const wordCount = chunk.trim().split(/\s+/).filter(word => word.length > 0).length;
    const meaningfulContent = chunk.replace(/[\s\n\r\t]+/g, ' ').trim();

    // Check for empty or whitespace-only content
    if (!meaningfulContent || meaningfulContent.length < 10) {
      const filterReason = 'Empty or whitespace-only content';
      filterReport.filtered.push({
        identifier: chunkId,
        reason: filterReason,
        chars: charCount,
        words: wordCount,
        content: chunk
      });
      filterReport.reasons.emptyContent++;

      // Log the filtered chunk
      logger.logChunkFiltering(documentInfo, {
        index: index,
        pageNumber: metadata.pageNumber,
        content: chunk,
        wordCount: wordCount,
        charCount: charCount,
        meaningfulRatio: 0,
        filterReason: filterReason
      });

      return;
    }

    // Check for tabular artifacts and formatting patterns
    const lines = chunk.split('\n').map(line => line.trim()).filter(line => line.length > 0);

    // Check if chunk is primarily table artifacts
    let tableArtifactLines = 0;
    for (const line of lines) {
      for (const pattern of filterCriteria.tableArtifactPatterns) {
        if (pattern.test(line)) {
          tableArtifactLines++;
          break;
        }
      }
    }

    if (lines.length > 0 && tableArtifactLines / lines.length > 0.6) {
      const filterReason = `Table formatting artifacts (${Math.round((tableArtifactLines / lines.length) * 100)}% artifact lines)`;
      filterReport.filtered.push({
        identifier: chunkId,
        reason: filterReason,
        chars: charCount,
        words: wordCount,
        content: chunk
      });
      filterReport.reasons.tableArtifacts = (filterReport.reasons.tableArtifacts || 0) + 1;

      // Log the filtered chunk
      logger.logChunkFiltering(documentInfo, {
        index: index,
        pageNumber: metadata.pageNumber,
        content: chunk,
        wordCount: wordCount,
        charCount: charCount,
        meaningfulRatio: meaningfulContent.length > 0 ? (meaningfulContent.match(/[a-zA-Z0-9]/g) || []).length / meaningfulContent.length : 0,
        filterReason: filterReason,
        matchedPattern: 'tableArtifactPatterns'
      });

      return;
    }

    // Check for navigation patterns (page numbers, TOC, etc.)
    for (const pattern of filterCriteria.navigationPatterns) {
      if (pattern.test(meaningfulContent)) {
        const filterReason = 'Navigation/structural element (page number, TOC, etc.)';
        filterReport.filtered.push({
          identifier: chunkId,
          reason: filterReason,
          chars: charCount,
          words: wordCount,
          content: chunk
        });
        filterReport.reasons.navigationElements = (filterReport.reasons.navigationElements || 0) + 1;

        // Log the filtered chunk
        logger.logChunkFiltering(documentInfo, {
          index: index,
          pageNumber: metadata.pageNumber,
          content: chunk,
          wordCount: wordCount,
          charCount: charCount,
          meaningfulRatio: meaningfulContent.length > 0 ? (meaningfulContent.match(/[a-zA-Z0-9]/g) || []).length / meaningfulContent.length : 0,
          filterReason: filterReason,
          matchedPattern: pattern.toString()
        });

        return;
      }
    }

    // Check for copyright patterns
    for (const pattern of filterCriteria.copyrightPatterns) {
      if (pattern.test(meaningfulContent)) {
        const filterReason = 'Copyright or legal notice';
        filterReport.filtered.push({
          identifier: chunkId,
          reason: filterReason,
          chars: charCount,
          words: wordCount,
          content: chunk
        });
        filterReport.reasons.copyrightNotices = (filterReport.reasons.copyrightNotices || 0) + 1;

        // Log the filtered chunk
        logger.logChunkFiltering(documentInfo, {
          index: index,
          pageNumber: metadata.pageNumber,
          content: chunk,
          wordCount: wordCount,
          charCount: charCount,
          meaningfulRatio: meaningfulContent.length > 0 ? (meaningfulContent.match(/[a-zA-Z0-9]/g) || []).length / meaningfulContent.length : 0,
          filterReason: filterReason,
          matchedPattern: pattern.toString()
        });

        return;
      }
    }

    // Check minimum character count
    if (charCount < filterCriteria.minChars) {
      const filterReason = `Too short (${charCount} < ${filterCriteria.minChars} chars)`;
      filterReport.filtered.push({
        identifier: chunkId,
        reason: filterReason,
        chars: charCount,
        words: wordCount,
        content: chunk
      });
      filterReport.reasons.tooShort++;

      // Log the filtered chunk
      logger.logChunkFiltering(documentInfo, {
        index: index,
        pageNumber: metadata.pageNumber,
        content: chunk,
        wordCount: wordCount,
        charCount: charCount,
        meaningfulRatio: meaningfulContent.length > 0 ? (meaningfulContent.match(/[a-zA-Z0-9]/g) || []).length / meaningfulContent.length : 0,
        filterReason: filterReason
      });

      return;
    }

    // Check minimum word count
    if (wordCount < filterCriteria.minWords) {
      const filterReason = `Low word count (${wordCount} < ${filterCriteria.minWords} words)`;
      filterReport.filtered.push({
        identifier: chunkId,
        reason: filterReason,
        chars: charCount,
        words: wordCount,
        content: chunk
      });
      filterReport.reasons.lowWordCount++;

      // Log the filtered chunk
      logger.logChunkFiltering(documentInfo, {
        index: index,
        pageNumber: metadata.pageNumber,
        content: chunk,
        wordCount: wordCount,
        charCount: charCount,
        meaningfulRatio: meaningfulContent.length > 0 ? (meaningfulContent.match(/[a-zA-Z0-9]/g) || []).length / meaningfulContent.length : 0,
        filterReason: filterReason
      });

      return;
    }

    // Check for meaningful content ratio (letters/numbers vs total)
    const meaningfulChars = (meaningfulContent.match(/[a-zA-Z0-9]/g) || []).length;
    const meaningfulRatio = meaningfulChars / meaningfulContent.length;

    if (meaningfulRatio < filterCriteria.minMeaningfulRatio) {
      const filterReason = `Low meaningful content ratio (${Math.round(meaningfulRatio * 100)}% < ${Math.round(filterCriteria.minMeaningfulRatio * 100)}%)`;
      filterReport.filtered.push({
        identifier: chunkId,
        reason: filterReason,
        chars: charCount,
        words: wordCount,
        content: chunk
      });
      filterReport.reasons.lowMeaningful++;

      // Log the filtered chunk
      logger.logChunkFiltering(documentInfo, {
        index: index,
        pageNumber: metadata.pageNumber,
        content: chunk,
        wordCount: wordCount,
        charCount: charCount,
        meaningfulRatio: meaningfulRatio,
        filterReason: filterReason
      });

      return;
    }

    // Check for high repetition (common in headers, footers, page numbers)
    const words = meaningfulContent.toLowerCase().split(/\s+/);
    const uniqueWords = new Set(words);
    const repetitionRatio = 1 - (uniqueWords.size / words.length);

    if (repetitionRatio > filterCriteria.maxRepetitionRatio && words.length > 5) {
      const filterReason = `High repetition ratio (${Math.round(repetitionRatio * 100)}% > ${Math.round(filterCriteria.maxRepetitionRatio * 100)}%)`;
      filterReport.filtered.push({
        identifier: chunkId,
        reason: filterReason,
        chars: charCount,
        words: wordCount,
        content: chunk
      });
      filterReport.reasons.highRepetition++;

      // Log the filtered chunk
      logger.logChunkFiltering(documentInfo, {
        index: index,
        pageNumber: metadata.pageNumber,
        content: chunk,
        wordCount: wordCount,
        charCount: charCount,
        meaningfulRatio: meaningfulRatio,
        repetitionRatio: repetitionRatio,
        filterReason: filterReason
      });

      return;
    }

    // Chunk passed all filters - keep it
    filteredChunks.push(chunk);

    // Update metadata with original index for tracking
    const updatedMetadata = {
      ...metadata,
      originalIndex: index,
      filteredIndex: filteredChunks.length - 1,
      qualityScore: {
        charCount,
        wordCount,
        meaningfulRatio: Math.round(meaningfulRatio * 100),
        repetitionRatio: Math.round(repetitionRatio * 100)
      }
    };
    filteredMetadata.push(updatedMetadata);
  });

  return {
    filteredChunks,
    filteredMetadata,
    filterReport
  };
}

/**
 * Helper function to process document for vector search
 */
async function processDocumentForVector({ documentId, appId, filename, userId, parsedText, pages = null, metadata = {} }) {
  console.log(`📄 Document: ${filename || documentId}`);
  console.log(`🏢 AppId: ${appId}`);
  console.log(`📊 Text length: ${parsedText.length} characters`);

  // Initialize Qdrant if not already done
  await qdrantService.initialize();

  // Step 1: Determine chunking strategy
  let chunks = [];
  let chunkMetadata = [];

  if (pages && Array.isArray(pages) && pages.length > 0) {
    // Use smart chunking for optimal content organization
    console.log('\n🔧 Step 1: Creating optimized semantic chunks');
    console.log('=============================================');

    // Use smart chunking service to create optimized chunks
    const smartChunkingResult = smartChunkingService.createOptimizedChunks(pages, {
      filename: filename || documentId,
      uploadId: documentId,
      totalPages: pages.length
    });

    chunks = smartChunkingResult.chunks.map(chunk => chunk.text);
    chunkMetadata = smartChunkingResult.metadata;

    console.log(`✅ Created ${chunks.length} optimized semantic chunks`);
    smartChunkingResult.chunks.forEach((chunk, index) => {
      console.log(`   🧠 Chunk ${index + 1}: ${chunk.charCount} chars, ${chunk.wordCount} words, pages ${chunk.pageRange.start}-${chunk.pageRange.end}`);
    });

    // Log smart chunking improvements
    const report = smartChunkingResult.report;
    console.log('\n📊 Smart Chunking Benefits:');
    console.log(`   💰 API call reduction: ${report.improvement.apiCallReduction} (${report.improvement.costSavings} savings)`);
    console.log(`   📈 Average chunk size: ${report.optimized.avgChunkSize} words`);
    console.log(`   🎯 Quality improvement: ${Math.round(report.quality.avgQualityScore * 100)}% meaningful content`);

  } else {
    // Fallback to artificial chunking for backward compatibility
    console.log('\n🔧 Step 1: Using artificial chunking (fallback)');
    console.log('⚠️  No page data available, falling back to text splitting');

    chunks = embeddingService.splitTextIntoChunks(parsedText, 1000, 100);
    chunkMetadata = chunks.map((chunk, index) => ({
      chunkType: 'artificial',
      chunkIndex: index,
      wordCount: embeddingService.countWords ? embeddingService.countWords(chunk) : chunk.split(/\s+/).length
    }));

    console.log(`📄 Split into ${chunks.length} artificial chunks`);
  }

  // Step 1.5: Filter out junk chunks to improve quality and reduce costs
  console.log('\n🧹 Step 1.5: Filtering junk chunks');
  console.log('==================================');

  const documentInfo = {
    filename: filename || documentId,
    uploadId: documentId
  };
  const { filteredChunks, filteredMetadata, filterReport } = filterJunkChunks(chunks, chunkMetadata, documentInfo);

  console.log(`📊 Chunk filtering results:`);
  console.log(`   ✅ High-quality chunks: ${filteredChunks.length}/${chunks.length}`);
  console.log(`   🗑️  Filtered junk chunks: ${filterReport.filtered.length}/${chunks.length}`);
  console.log(`   💰 Cost savings: ${Math.round((filterReport.filtered.length / chunks.length) * 100)}% fewer embeddings`);

  // Calculate content statistics
  const totalCharsFiltered = filterReport.filtered.reduce((sum, item) => sum + (item.chars || 0), 0);
  const totalWordsFiltered = filterReport.filtered.reduce((sum, item) => sum + (item.words || 0), 0);

  console.log(`\n📊 Content Filtering Summary:`);
  console.log(`   📝 Characters filtered: ${totalCharsFiltered.toLocaleString()}`);
  console.log(`   📝 Words filtered: ${totalWordsFiltered.toLocaleString()}`);
  console.log(`   💾 Storage saved: ~${Math.round(totalCharsFiltered / 1024)} KB of text`);
  console.log(`   🔍 Quality improvement: Removed low-value content for better search results`);

  if (filterReport.filtered.length > 0) {
    console.log(`\n🗑️  Filtered chunks details:`);
    filterReport.filtered.forEach((item, index) => {
      const preview = item.content ? item.content.substring(0, 60).replace(/\n/g, ' ') + '...' : 'N/A';
      console.log(`   📄 ${item.identifier}: ${item.reason}`);
      console.log(`      📊 Stats: ${item.chars} chars, ${item.words} words`);
      console.log(`      📝 Content: "${preview}"`);
      if (index < filterReport.filtered.length - 1) console.log('');
    });

    // Show filtering summary by reason
    console.log(`\n📊 Filtering breakdown:`);
    Object.entries(filterReport.reasons).forEach(([reason, count]) => {
      if (count > 0) {
        console.log(`   ${reason}: ${count} chunks`);
      }
    });
  }

  // Update chunks and metadata to use filtered versions
  chunks = filteredChunks;
  chunkMetadata = filteredMetadata;

  // Step 2: Generate embeddings for all chunks using optimized batch processing
  console.log('\n🚀 Step 2: Generating embeddings (optimized batch processing)');
  // Use optimized batch processing with parallel execution for better performance
  const allEmbeddings = await embeddingService.generateBatchEmbeddings(chunks, null, 3, true);
  console.log(`✅ Generated embeddings for all ${chunks.length} chunks`);

  // Step 3: Store all chunks in Qdrant using optimized batch storage
  console.log('\n💾 Step 3: Storing chunks in Qdrant (optimized batch storage)');

  // Prepare documents with embeddings for batch storage
  const documentsWithEmbeddings = chunks.map((chunk, i) => {
    const embeddings = allEmbeddings[i];

    if (!embeddings) {
      return { document: null, embeddings: null }; // Will be filtered out
    }

    const enhancedMetadata = {
      appId,
      documentId,
      filename: filename || documentId,
      userId,
      chunkIndex: i,
      totalChunks: chunks.length,
      ...metadata,
      ...chunkMetadata[i] // Add page-specific or chunk-specific metadata
    };

    return {
      document: {
        text: chunk,
        metadata: enhancedMetadata
      },
      embeddings: embeddings
    };
  }).filter(item => item.embeddings !== null); // Remove items without embeddings

  console.log(`📦 Prepared ${documentsWithEmbeddings.length}/${chunks.length} chunks for batch storage`);

  // Use optimized batch storage (50 chunks per batch for optimal performance)
  const batchResult = await qdrantService.storeDocumentsBatch(documentsWithEmbeddings, 50);

  const storedChunks = batchResult.storedChunks;
  const failedChunks = batchResult.failedChunks;

  // Add any chunks that failed during embedding generation
  const embeddingFailures = chunks.length - documentsWithEmbeddings.length;
  if (embeddingFailures > 0) {
    console.log(`⚠️ ${embeddingFailures} chunks failed during embedding generation`);
    for (let i = 0; i < chunks.length; i++) {
      if (!allEmbeddings[i]) {
        failedChunks.push({
          chunkIndex: i,
          reason: 'No embeddings - batch processing failed'
        });
      }
    }
  }

  // Report optimized storage results
  console.log(`\n📊 Optimized Storage Summary:`);
  console.log(`✅ Successfully stored: ${storedChunks.length}/${chunks.length} chunks`);
  console.log(`❌ Failed to store: ${failedChunks.length}/${chunks.length} chunks`);
  console.log(`🚀 Success rate: ${batchResult.successRate}%`);
  console.log(`⚡ Performance: Batch storage optimization enabled`);

  if (failedChunks.length > 0) {
    console.log(`\n💥 Failed chunk details:`);
    failedChunks.forEach(failed => {
      console.log(`   Chunk ${failed.chunkIndex + 1}: ${failed.reason}`);
    });
  }

  const chunkingStrategy = chunkMetadata[0]?.chunkType || 'unknown';
  const pageBasedChunks = chunkingStrategy === 'page' ? chunks.length : 0;

  console.log(`\n✅ Document processing complete`);
  console.log(`📊 Chunking strategy: ${chunkingStrategy}`);
  console.log(`🧹 Quality filtering: ${filterReport.filtered.length} junk chunks removed`);
  console.log(`💰 Cost optimization: ${Math.round((filterReport.filtered.length / (chunks.length + filterReport.filtered.length)) * 100)}% embedding quota saved`);
  if (pageBasedChunks > 0) {
    console.log(`📄 High-quality chunks processed: ${pageBasedChunks}`);
  }

  // Determine overall success status
  const successRate = storedChunks.length / chunks.length;
  const isFullSuccess = successRate === 1.0;
  const isPartialSuccess = successRate >= 0.8; // 80% threshold for partial success

  let status, message;
  if (isFullSuccess) {
    status = 'success';
    message = `Document processed successfully - all ${chunks.length} ${chunkingStrategy} chunks stored`;
  } else if (isPartialSuccess) {
    status = 'partial_success';
    message = `Document processed with ${Math.round(successRate * 100)}% success rate - ${storedChunks.length}/${chunks.length} ${chunkingStrategy} chunks stored`;
  } else {
    status = 'failed';
    message = `Document processing failed - only ${storedChunks.length}/${chunks.length} ${chunkingStrategy} chunks stored (${Math.round(successRate * 100)}%)`;
  }

  console.log(`📊 Final Status: ${status.toUpperCase()}`);
  console.log(`📊 ${message}`);

  return {
    status,
    message,
    totalChunks: chunks.length,
    storedChunks: storedChunks.length,
    failedChunks: failedChunks.length,
    chunkingStrategy: chunkingStrategy,
    pageBasedChunks: pageBasedChunks,
    successRate: Math.round(successRate * 100),
    chunks: storedChunks,
    failedChunkDetails: failedChunks,
    qualityFiltering: {
      originalChunks: chunks.length + filterReport.filtered.length,
      filteredChunks: chunks.length,
      junkChunksRemoved: filterReport.filtered.length,
      costSavingsPercent: Math.round((filterReport.filtered.length / (chunks.length + filterReport.filtered.length)) * 100),
      filterReasons: filterReport.reasons
    },
    success: isPartialSuccess // Consider partial success as success for backward compatibility
  };
}

/**
 * Delete document from vector database
 * Called when document is deleted from User-Service
 * Requires internal API key for security
 */
router.delete('/document/:documentId', async (req, res) => {
  try {
    const { documentId } = req.params;
    const { appId } = req.query;

    // Verify internal API key for security
    const expectedKey = process.env.INTERNAL_API_KEY || 'chatai-internal-2024';
    const internalApiKey = req.headers['x-internal-api-key'];

    if (internalApiKey !== expectedKey) {
      return res.status(401).json({
        success: false,
        error: 'Unauthorized: Invalid internal API key'
      });
    }

    if (!documentId || !appId) {
      return res.status(400).json({
        success: false,
        error: 'Missing documentId or appId'
      });
    }

    console.log(`🗑️ Deleting document ${documentId} from vector database for app ${appId}`);

    await qdrantService.initialize();

    // Delete all chunks for this document
    const deletedCount = await qdrantService.deleteDocument(documentId, appId);

    console.log(`✅ Deleted ${deletedCount} chunks for document ${documentId}`);

    res.json({
      success: true,
      message: 'Document deleted from vector database',
      data: {
        documentId,
        appId,
        deletedChunks: deletedCount
      }
    });

  } catch (error) {
    console.error('❌ Vector deletion error:', error);
    res.status(500).json({
      success: false,
      error: 'Vector deletion failed',
      message: error.message
    });
  }
});

/**
 * Get vector database stats for an app
 */
router.get('/stats/:appId', async (req, res) => {
  try {
    const { appId } = req.params;

    await qdrantService.initialize();

    const stats = await qdrantService.getAppStats(appId);

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('❌ Vector stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get vector stats',
      message: error.message
    });
  }
});

/**
 * Get full parsed chunks for a document
 * This endpoint provides the complete parsed chunks without preview truncation
 */
router.get('/document/:documentId/full-chunks', async (req, res) => {
  try {
    const { documentId } = req.params;
    const { appId } = req.query;

    // Verify internal API key for security
    const expectedKey = process.env.INTERNAL_API_KEY || 'chatai-internal-2024';
    const internalApiKey = req.headers['x-internal-api-key'];

    if (internalApiKey !== expectedKey) {
      return res.status(401).json({
        success: false,
        error: 'Unauthorized: Invalid internal API key'
      });
    }

    if (!documentId || !appId) {
      return res.status(400).json({
        success: false,
        error: 'Missing documentId or appId'
      });
    }

    // Initialize Qdrant to get document chunks
    await qdrantService.initialize();

    // Get document chunks to reconstruct full content
    const chunks = await qdrantService.searchSimilar('', appId, 1000, {
      must: [
        { key: 'documentId', match: { value: documentId } }
      ]
    });

    if (!chunks || chunks.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Document not found or no content available'
      });
    }

    // Sort chunks by their original order and create full chunks structure
    const sortedChunks = chunks
      .filter(chunk => chunk.payload?.contentType !== 'full_document_text') // Exclude metadata entries
      .sort((a, b) => {
        const aIndex = a.payload?.chunkIndex || a.payload?.pageNumber || 0;
        const bIndex = b.payload?.chunkIndex || b.payload?.pageNumber || 0;
        return aIndex - bIndex;
      });

    const fullChunks = sortedChunks.map((chunk, index) => ({
      page: chunk.payload?.pageNumber || index + 1,
      text: chunk.payload?.text || '',
      wordCount: chunk.payload?.wordCount || countWords(chunk.payload?.text || ''),
      characterCount: (chunk.payload?.text || '').length,
      hasImages: chunk.payload?.hasImages || false,
      imageCount: chunk.payload?.imageCount || 0,
      isMerged: chunk.payload?.isMerged || false,
      mergedFrom: chunk.payload?.mergedFrom || null,
      chunkType: chunk.payload?.chunkType || 'unknown'
    }));

    res.json({
      success: true,
      data: {
        documentId,
        appId,
        chunks: fullChunks,
        totalPages: fullChunks.length,
        totalCharacters: fullChunks.reduce((sum, chunk) => sum + chunk.characterCount, 0),
        totalWords: fullChunks.reduce((sum, chunk) => sum + chunk.wordCount, 0),
        retrievalMethod: 'reconstructed_from_vector_chunks'
      }
    });

  } catch (error) {
    console.error('❌ Full chunks retrieval error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve full chunks',
      message: error.message
    });
  }
});

/**
 * Health check for vector processing
 */
router.get('/health', async (req, res) => {
  try {
    await qdrantService.initialize();

    const embeddingInfo = embeddingService.getProviderInfo();

    const health = {
      status: 'healthy',
      qdrant: qdrantService.isInitialized ? 'connected' : 'disconnected',
      embedding: {
        provider: embeddingInfo.provider,
        model: embeddingInfo.model,
        dimensions: embeddingInfo.dimensions,
        configured: embeddingInfo.isConfigured
      },
      timestamp: new Date().toISOString()
    };

    res.json({
      success: true,
      data: health
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Vector processing unhealthy',
      message: error.message
    });
  }
});

/**
 * Update document status directly in database (preferred method)
 * @param {string} appId - Application ID
 * @param {string} documentId - Document ID
 * @param {string} status - New status (parsing, processing, embedding, indexing, ready, error)
 * @param {string} message - Status message
 * @param {Object} additionalData - Additional data (parsedData, pageCount, etc.)
 */
async function updateDocumentStatusDirect(appId, documentId, status, message, additionalData = {}) {
  try {
    console.log(`📡 Updating database directly: Document ${documentId} → ${status}`);
    console.log(`💬 Message: ${message}`);

    // Prepare data for database update (don't include message as it's not in schema)
    const updateData = {
      ...additionalData,
    };

    // Update directly in database
    const result = await databaseService.updateDocumentStatus(documentId, appId, status, updateData);

    console.log(`✅ Database status updated successfully: ${status}`);
    return result;

  } catch (error) {
    console.warn(`⚠️ Direct database update failed: ${error.message}`);

    // Log the error but don't fallback to HTTP API since we removed that endpoint
    console.error(`❌ Database update failed for document ${documentId}: ${error.message}`);

    // For now, just log the failure and continue processing
    // The document will still be processed, just status won't be updated
    console.log(`⚠️ Continuing processing despite status update failure...`);
    return { success: false, error: error.message };
  }
}

/**
 * Notify User-Service about status updates via internal API (legacy HTTP method)
 * @param {string} appId - Application ID
 * @param {string} documentId - Document ID
 * @param {string} status - New status (parsing, embedding, indexing, ready, error)
 * @param {string} message - Status message
 * @param {Object} additionalData - Additional data (parsedData, pageCount, etc.)
 */
async function notifyUserServiceStatus(appId, documentId, status, message, additionalData = {}) {
  try {
    const userServiceUrl = process.env.USER_SERVICE_URL || 'http://localhost:3000';
    const internalApiKey = process.env.INTERNAL_API_KEY || 'chatai-internal-2024';

    console.log(`📡 Updating User-Service: Document ${documentId} → ${status}`);
    console.log(`💬 Message: ${message}`);

    const updateData = {
      appId,
      documentId,
      status,
      message,
      ...additionalData
    };

    const response = await fetch(`${userServiceUrl}/users/app/chatai/internal/update-document-status`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-internal-api-key': internalApiKey
      },
      body: JSON.stringify(updateData)
    });

    if (response.ok) {
      const result = await response.json();
      console.log(`✅ User-Service status updated: ${status}`);
      return result;
    } else {
      const errorText = await response.text();
      console.warn(`⚠️ Failed to update User-Service: ${response.status} - ${errorText}`);
      return null;
    }

  } catch (error) {
    console.warn(`⚠️ Status notification failed: ${error.message}`);
    // Don't throw error - status notification is optional
    return null;
  }
}

module.exports = router;

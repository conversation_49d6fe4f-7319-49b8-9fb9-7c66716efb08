const express = require('express');
const cors = require('cors');
const config = require('./config');
const databaseService = require('./services/databaseService');
const { validateDatabaseConfig } = require('./config/database');

const app = express();

// Middleware
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Origin', 'X-Requested-With', 'Accept'],
  credentials: true
}));

app.use(express.json({ limit: '25mb' }));
app.use(express.urlencoded({ extended: true, limit: '25mb' }));

// Routes
const routes = require('./routes');
app.use('/', routes);

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('❌ Unhandled error:', err);
  res.status(500).json({
    error: true,
    message: 'Internal server error'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: true,
    message: 'Endpoint not found'
  });
});

const PORT = config.port || 3002;

// Initialize server with database connection
async function startServer() {
  try {
    // Validate database configuration
    validateDatabaseConfig();

    // Initialize database connection
    console.log('🔌 Initializing database connection...');
    await databaseService.initialize();

    // Start Express server
    app.listen(PORT, () => {
      console.log(`🚀 ChatAI SDK Clean running on port ${PORT}`);
      console.log(`📋 Available endpoints:`);
      console.log(`   GET  /api/v1/?apikey=...&query=...  - Main chat endpoint`);
      console.log(`   POST /api/vector/upload-and-process - Document upload`);
      console.log(`   GET  /health                        - Health check`);
      console.log(`\n🔧 Configuration:`);
      console.log(`   User Service: ${config.userService.url}`);
      console.log(`   Qdrant Vector DB: ${config.qdrant.url}`);
      console.log(`   PostgreSQL: Connected ✅`);
      console.log(`   OpenRouter: ${config.openRouter.baseUrl}`);
    });

  } catch (error) {
    console.error('❌ Failed to start server:', error.message);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('🛑 Received SIGTERM, shutting down gracefully...');
  await databaseService.close();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('🛑 Received SIGINT, shutting down gracefully...');
  await databaseService.close();
  process.exit(0);
});

// Start the server
startServer();

module.exports = app;

// ChatAI Project Entity (read-only for validation)
const { EntitySchema } = require('typeorm');

const ChatAi = new EntitySchema({
  name: 'ChatA<PERSON>',
  tableName: 'chat_ai_projects',
  columns: {
    id: {
      type: 'uuid',
      primary: true,
      generated: 'uuid',
    },
    name: {
      type: 'varchar',
      nullable: false,
    },
    description: {
      type: 'text',
      nullable: true,
    },
    userId: {
      type: 'varchar',
      nullable: false,
      comment: 'Supabase user ID',
    },
    appId: {
      type: 'varchar',
      nullable: false,
      comment: 'References Application ID',
    },
    isActive: {
      type: 'boolean',
      nullable: false,
      default: true,
    },
    createdAt: {
      type: 'timestamp',
      default: () => 'CURRENT_TIMESTAMP',
      createDate: true,
    },
    updatedAt: {
      type: 'timestamp',
      default: () => 'CURRENT_TIMESTAMP',
      updateDate: true,
    },
  },
  relations: {
    documents: {
      type: 'one-to-many',
      target: 'ChatAiDocument',
      inverseSide: 'project',
    },
    messages: {
      type: 'one-to-many',
      target: 'ChatAiMessage',
      inverseSide: 'chatAi',
    },
  },
  indices: [
    {
      name: 'IDX_CHATAI_USER_APP',
      columns: ['userId', 'appId'],
    },
    {
      name: 'IDX_CHATAI_APP_ACTIVE',
      columns: ['appId', 'isActive'],
    },
  ],
});

module.exports = ChatAi;

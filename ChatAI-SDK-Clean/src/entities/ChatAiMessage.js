// ChatAI Message Entity for chat storage
const { EntitySchema } = require('typeorm');

const ChatAiMessage = new EntitySchema({
  name: 'ChatAiMessage',
  tableName: 'chat_ai_messages',
  columns: {
    id: {
      type: 'int',
      primary: true,
      generated: true,
    },
    question: {
      type: 'text',
      nullable: false,
      comment: 'User question/prompt',
    },
    response: {
      type: 'text',
      nullable: false,
      comment: 'AI assistant response',
    },
    sourceReferences: {
      type: 'jsonb',
      nullable: true,
      comment: 'Array of page references from multiple docs',
    },
    isAdminDebug: {
      type: 'boolean',
      nullable: false,
      default: false,
      comment: 'Mark admin debug sessions',
    },
    timestamp: {
      type: 'timestamp',
      default: () => 'CURRENT_TIMESTAMP',
      createDate: true,
    },
    chatAiId: {
      type: 'varchar',
      nullable: false,
      comment: 'References ChatAi project ID',
    },
    documentId: {
      type: 'int',
      nullable: true,
      comment: 'Optional - if question relates to specific doc',
    },
  },
  relations: {
    chatAi: {
      type: 'many-to-one',
      target: 'ChatAi',
      joinColumn: { name: 'chatAiId' },
      onDelete: 'CASCADE',
    },
    document: {
      type: 'many-to-one',
      target: 'ChatAiDocument',
      joinColumn: { name: 'documentId' },
      onDelete: 'SET NULL',
      nullable: true,
    },
  },
  // Indices removed to match existing schema
  // indices: [],
});

module.exports = ChatAiMessage;

// ChatAI Message Entity for chat storage
const { EntitySchema } = require('typeorm');

const ChatAiMessage = new EntitySchema({
  name: 'ChatAiMessage',
  tableName: 'chat_ai_messages',
  columns: {
    id: {
      type: 'int',
      primary: true,
      generated: true,
    },
    question: {
      type: 'text',
      nullable: false,
      comment: 'User question/prompt',
    },
    response: {
      type: 'text',
      nullable: false,
      comment: 'AI assistant response',
    },
    sourceReferences: {
      type: 'jsonb',
      nullable: true,
      comment: 'Array of page references from multiple docs',
    },
    isAdminDebug: {
      type: 'boolean',
      nullable: false,
      default: false,
      comment: 'Mark admin debug sessions',
    },
    timestamp: {
      type: 'timestamp',
      default: () => 'CURRENT_TIMESTAMP',
      createDate: true,
    },
    chatAiId: {
      type: 'varchar',
      nullable: false,
      comment: 'References ChatAi project ID',
    },
    documentId: {
      type: 'int',
      nullable: true,
      comment: 'Optional - if question relates to specific doc',
    },
    // Additional fields for enhanced chat functionality
    sessionId: {
      type: 'varchar',
      nullable: true,
      comment: 'Chat session identifier for grouping messages',
    },
    userId: {
      type: 'varchar',
      nullable: false,
      comment: 'User who sent the message',
    },
    role: {
      type: 'varchar',
      nullable: false,
      default: 'user',
      comment: 'Message role: user, assistant, system',
    },
    metadata: {
      type: 'jsonb',
      nullable: true,
      comment: 'Additional metadata (tokens used, model, etc.)',
    },
    responseTimeMs: {
      type: 'int',
      nullable: true,
      comment: 'Time taken to generate response in milliseconds',
    },
    tokensUsed: {
      type: 'int',
      nullable: true,
      comment: 'Number of tokens used for this message',
    },
    model: {
      type: 'varchar',
      nullable: true,
      comment: 'AI model used for response generation',
    },
    contextDocuments: {
      type: 'jsonb',
      nullable: true,
      comment: 'Documents used as context for this response',
    },
  },
  relations: {
    chatAi: {
      type: 'many-to-one',
      target: 'ChatAi',
      joinColumn: { name: 'chatAiId' },
      onDelete: 'CASCADE',
    },
    document: {
      type: 'many-to-one',
      target: 'ChatAiDocument',
      joinColumn: { name: 'documentId' },
      onDelete: 'SET NULL',
      nullable: true,
    },
  },
  indices: [
    {
      name: 'IDX_MESSAGE_CHATAI_TIMESTAMP',
      columns: ['chatAiId', 'timestamp'],
    },
    {
      name: 'IDX_MESSAGE_USER_TIMESTAMP',
      columns: ['userId', 'timestamp'],
    },
    {
      name: 'IDX_MESSAGE_SESSION',
      columns: ['sessionId'],
    },
    {
      name: 'IDX_MESSAGE_DOCUMENT',
      columns: ['documentId'],
    },
  ],
});

module.exports = ChatAiMessage;

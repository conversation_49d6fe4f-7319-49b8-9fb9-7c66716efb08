// Database service for ChatAI-SDK-Clean
const { DataSource } = require('typeorm');
const { databaseConfig } = require('../config/database');

// Import entities
const ChatAiDocument = require('../entities/ChatAiDocument');
const ChatAiMessage = require('../entities/ChatAiMessage');
const ChatAi = require('../entities/ChatAi');

class DatabaseService {
  constructor() {
    this.dataSource = null;
    this.isConnected = false;
    this.connectionAttempts = 0;
    this.maxRetries = 3;
  }

  /**
   * Initialize database connection
   */
  async initialize() {
    try {
      console.log('🔌 Initializing database connection...');

      this.dataSource = new DataSource({
        ...databaseConfig,
        entities: [ChatAiDocument, ChatAiMessage, ChatAi],
      });

      await this.dataSource.initialize();
      this.isConnected = true;
      this.connectionAttempts = 0;

      console.log('✅ Database connection established successfully');
      console.log(`📊 Connected to: ${databaseConfig.host}:${databaseConfig.port}/${databaseConfig.database}`);

      // Test connection with a simple query
      await this.testConnection();

    } catch (error) {
      this.connectionAttempts++;
      console.error(`❌ Database connection failed (attempt ${this.connectionAttempts}/${this.maxRetries}):`, error.message);

      if (this.connectionAttempts < this.maxRetries) {
        console.log(`🔄 Retrying connection in 3 seconds...`);
        setTimeout(() => this.initialize(), 3000);
      } else {
        throw new Error(`Failed to connect to database after ${this.maxRetries} attempts: ${error.message}`);
      }
    }
  }

  /**
   * Test database connection
   */
  async testConnection() {
    try {
      const result = await this.dataSource.query('SELECT NOW() as current_time');
      console.log(`🕒 Database time: ${result[0].current_time}`);
      return true;
    } catch (error) {
      console.error('❌ Database connection test failed:', error.message);
      throw error;
    }
  }

  /**
   * Update document status with additional data
   */
  async updateDocumentStatus(documentId, appId, status, additionalData = {}) {
    try {
      if (!this.isConnected) {
        throw new Error('Database not connected');
      }

      const documentRepo = this.dataSource.getRepository('ChatAiDocument');

      // Find document with security validation (appId check)
      const document = await documentRepo
        .createQueryBuilder('document')
        .leftJoin('document.project', 'project')
        .where('document.id = :documentId', { documentId })
        .andWhere('project.appId = :appId', { appId })
        .getOne();

      if (!document) {
        throw new Error(`Document not found or access denied: ${documentId} for app ${appId}`);
      }

      // Prepare update data (only use fields that exist in the schema)
      const updateFields = {
        status,
        ...additionalData,
      };

      // Remove any fields that don't exist in the actual schema
      delete updateFields.lastUpdated;
      delete updateFields.processingStartedAt;
      delete updateFields.processingCompletedAt;
      delete updateFields.chunkCount;
      delete updateFields.vectorCount;
      delete updateFields.processingTimeMs;

      // Update document
      const result = await documentRepo.update(documentId, updateFields);

      if (result.affected === 0) {
        throw new Error(`No document updated for ID: ${documentId}`);
      }

      console.log(`✅ Document status updated: ${documentId} → ${status}`);

      // Log additional data if present
      if (Object.keys(additionalData).length > 0) {
        const logData = { ...additionalData };
        // Don't log large data fields
        if (logData.parsedData) logData.parsedData = '[PARSED_DATA]';
        console.log(`📊 Additional data:`, JSON.stringify(logData, null, 2));
      }

      return {
        success: true,
        documentId,
        status,
        updatedFields: Object.keys(updateFields),
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error(`❌ Database update failed for document ${documentId}:`, error.message);
      throw error;
    }
  }

  /**
   * Save chat message
   */
  async saveChatMessage(messageData) {
    try {
      if (!this.isConnected) {
        throw new Error('Database not connected');
      }

      const messageRepo = this.dataSource.getRepository('ChatAiMessage');

      // Validate required fields
      const requiredFields = ['question', 'response', 'chatAiId', 'userId'];
      const missingFields = requiredFields.filter(field => !messageData[field]);

      if (missingFields.length > 0) {
        throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
      }

      // Create message with timestamp
      const messageToSave = {
        ...messageData,
        timestamp: new Date(),
      };

      const message = messageRepo.create(messageToSave);
      const savedMessage = await messageRepo.save(message);

      console.log(`✅ Chat message saved: ${savedMessage.id} (session: ${savedMessage.sessionId || 'none'})`);
      return savedMessage;

    } catch (error) {
      console.error(`❌ Failed to save chat message:`, error.message);
      throw error;
    }
  }

  /**
   * Get chat history for a ChatAI project
   */
  async getChatHistory(chatAiId, userId, limit = 50, sessionId = null) {
    try {
      if (!this.isConnected) {
        throw new Error('Database not connected');
      }

      const messageRepo = this.dataSource.getRepository('ChatAiMessage');

      const queryBuilder = messageRepo
        .createQueryBuilder('message')
        .where('message.chatAiId = :chatAiId', { chatAiId })
        .andWhere('message.userId = :userId', { userId });

      if (sessionId) {
        queryBuilder.andWhere('message.sessionId = :sessionId', { sessionId });
      }

      const messages = await queryBuilder
        .orderBy('message.timestamp', 'DESC')
        .limit(limit)
        .getMany();

      console.log(`📜 Retrieved ${messages.length} chat messages for ChatAI ${chatAiId}`);
      return messages.reverse(); // Return in chronological order

    } catch (error) {
      console.error(`❌ Failed to get chat history:`, error.message);
      throw error;
    }
  }

  /**
   * Get document by ID with validation
   */
  async getDocument(documentId, appId) {
    try {
      if (!this.isConnected) {
        throw new Error('Database not connected');
      }

      const documentRepo = this.dataSource.getRepository('ChatAiDocument');

      const document = await documentRepo
        .createQueryBuilder('document')
        .leftJoin('document.project', 'project')
        .where('document.id = :documentId', { documentId })
        .andWhere('project.appId = :appId', { appId })
        .getOne();

      if (!document) {
        throw new Error(`Document not found: ${documentId} for app ${appId}`);
      }

      return document;

    } catch (error) {
      console.error(`❌ Failed to get document:`, error.message);
      throw error;
    }
  }

  /**
   * Get documents by status
   */
  async getDocumentsByStatus(appId, status, limit = 100) {
    try {
      if (!this.isConnected) {
        throw new Error('Database not connected');
      }

      const documentRepo = this.dataSource.getRepository('ChatAiDocument');

      const documents = await documentRepo
        .createQueryBuilder('document')
        .leftJoin('document.project', 'project')
        .where('project.appId = :appId', { appId })
        .andWhere('document.status = :status', { status })
        .orderBy('document.createdAt', 'DESC')
        .limit(limit)
        .getMany();

      return documents;

    } catch (error) {
      console.error(`❌ Failed to get documents by status:`, error.message);
      throw error;
    }
  }

  /**
   * Health check
   */
  async healthCheck() {
    try {
      if (!this.isConnected) {
        return { status: 'disconnected', error: 'Database not connected' };
      }

      await this.testConnection();

      return {
        status: 'healthy',
        connected: true,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      return {
        status: 'unhealthy',
        connected: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Close database connection
   */
  async close() {
    try {
      if (this.dataSource && this.isConnected) {
        await this.dataSource.destroy();
        this.isConnected = false;
        console.log('✅ Database connection closed gracefully');
      }
    } catch (error) {
      console.error('❌ Error closing database connection:', error.message);
    }
  }

  /**
   * Get connection status
   */
  getStatus() {
    return {
      connected: this.isConnected,
      attempts: this.connectionAttempts,
      maxRetries: this.maxRetries,
    };
  }
}

// Export singleton instance
module.exports = new DatabaseService();

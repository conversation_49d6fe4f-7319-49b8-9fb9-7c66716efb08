// Enhanced Document Processing with Direct Database Status Updates
// This replaces the current vectorProcessing.js logic

const databaseService = require('../services/databaseService');

/**
 * Enhanced document processing with direct database status updates
 */
async function processDocumentWithDirectStatusUpdates(req, res) {
  const { appId, documentId, filename } = req.body;
  const file = req.file;

  if (!file || !appId || !documentId) {
    return res.status(400).json({
      error: 'Missing required fields: file, appId, or documentId'
    });
  }

  try {
    const actualFilename = filename || file.originalname;
    console.log(`\n🚀 Starting document processing pipeline`);
    console.log(`📄 File: ${actualFilename}`);
    console.log(`🏢 AppId: ${appId}`);
    console.log(`📊 File size: ${file.size} bytes`);
    console.log(`🆔 Document ID: ${documentId}`);

    // ==========================================
    // STEP 1: UPDATE STATUS TO PARSING
    // ==========================================
    console.log('\n🔧 Step 1: Starting document parsing');
    console.log('=====================================');
    
    await updateDocumentStatusDirect(appId, documentId, 'parsing', {
      message: 'LlamaIndex parsing in progress',
      startedAt: new Date().toISOString(),
      filename: actualFilename,
      filesize: file.size,
      contentType: file.mimetype
    });

    // Parse document with LlamaIndex
    const parseResult = await llamaParseService.parseFile(file.buffer, actualFilename);

    if (parseResult.status !== 'SUCCESS' || !parseResult.result) {
      await updateDocumentStatusDirect(appId, documentId, 'error', {
        message: 'Document parsing failed',
        errorDetails: parseResult.error || 'Unknown parsing error',
        failedAt: new Date().toISOString()
      });
      throw new Error('Document parsing failed');
    }

    console.log(`✅ Document parsed successfully`);
    console.log(`📊 Text length: ${parseResult.result.text.length} characters`);

    // ==========================================
    // STEP 2: UPDATE STATUS TO PROCESSING
    // ==========================================
    console.log('\n🔧 Step 2: Processing and chunking document');
    console.log('============================================');

    await updateDocumentStatusDirect(appId, documentId, 'processing', {
      message: 'Processing parsed content and creating chunks',
      parsedData: {
        text: parseResult.result.text,
        pages: parseResult.result.pages || [],
        metadata: parseResult.result.metadata || {}
      },
      pageCount: parseResult.result.pages?.length || 0,
      wordCount: parseResult.result.text.split(/\s+/).length
    });

    // Process and chunk the document
    const processedChunks = await processDocumentContent(parseResult.result, actualFilename);
    
    if (!processedChunks || processedChunks.length === 0) {
      await updateDocumentStatusDirect(appId, documentId, 'error', {
        message: 'No valid chunks created from document',
        errorDetails: 'Document processing resulted in no usable content',
        failedAt: new Date().toISOString()
      });
      throw new Error('No valid chunks created');
    }

    console.log(`✅ Created ${processedChunks.length} chunks`);

    // ==========================================
    // STEP 3: UPDATE STATUS TO EMBEDDING
    // ==========================================
    console.log('\n🔧 Step 3: Generating embeddings');
    console.log('==================================');

    await updateDocumentStatusDirect(appId, documentId, 'embedding', {
      message: `Generating embeddings for ${processedChunks.length} chunks`,
      chunkCount: processedChunks.length,
      embeddingStartedAt: new Date().toISOString()
    });

    // Generate embeddings for all chunks
    const embeddedChunks = await generateEmbeddingsForChunks(processedChunks);

    if (!embeddedChunks || embeddedChunks.length === 0) {
      await updateDocumentStatusDirect(appId, documentId, 'error', {
        message: 'Embedding generation failed',
        errorDetails: 'Failed to generate embeddings for document chunks',
        failedAt: new Date().toISOString()
      });
      throw new Error('Embedding generation failed');
    }

    console.log(`✅ Generated embeddings for ${embeddedChunks.length} chunks`);

    // ==========================================
    // STEP 4: UPDATE STATUS TO INDEXING
    // ==========================================
    console.log('\n🔧 Step 4: Storing in vector database');
    console.log('======================================');

    await updateDocumentStatusDirect(appId, documentId, 'indexing', {
      message: `Storing ${embeddedChunks.length} vectors in Qdrant database`,
      vectorCount: embeddedChunks.length,
      indexingStartedAt: new Date().toISOString()
    });

    // Store vectors in Qdrant
    const indexResult = await storeVectorsInQdrant(embeddedChunks, appId, documentId, actualFilename);

    if (!indexResult.success) {
      await updateDocumentStatusDirect(appId, documentId, 'error', {
        message: 'Vector storage failed',
        errorDetails: indexResult.error || 'Failed to store vectors in Qdrant',
        failedAt: new Date().toISOString()
      });
      throw new Error('Vector storage failed');
    }

    console.log(`✅ Stored ${indexResult.vectorCount} vectors in Qdrant`);

    // ==========================================
    // STEP 5: UPDATE STATUS TO READY
    // ==========================================
    console.log('\n🔧 Step 5: Finalizing document');
    console.log('===============================');

    await updateDocumentStatusDirect(appId, documentId, 'ready', {
      message: 'Document processing completed successfully',
      completedAt: new Date().toISOString(),
      indexId: indexResult.indexId,
      vectorCount: indexResult.vectorCount,
      summary: {
        filename: actualFilename,
        filesize: file.size,
        contentType: file.mimetype,
        pageCount: parseResult.result.pages?.length || 0,
        wordCount: parseResult.result.text.split(/\s+/).length,
        chunkCount: processedChunks.length,
        vectorCount: indexResult.vectorCount,
        processingTimeMs: Date.now() - startTime
      }
    });

    console.log(`\n🎉 Document processing pipeline completed successfully!`);
    console.log(`📄 Document: ${actualFilename}`);
    console.log(`🆔 Document ID: ${documentId}`);
    console.log(`📊 Final stats: ${indexResult.vectorCount} vectors stored`);

    // Return success response
    res.json({
      success: true,
      message: 'Document processed successfully',
      documentId,
      filename: actualFilename,
      stats: {
        pageCount: parseResult.result.pages?.length || 0,
        wordCount: parseResult.result.text.split(/\s+/).length,
        chunkCount: processedChunks.length,
        vectorCount: indexResult.vectorCount
      }
    });

  } catch (error) {
    console.error(`❌ Document processing failed: ${error.message}`);
    
    // Update status to error with detailed information
    try {
      await updateDocumentStatusDirect(appId, documentId, 'error', {
        message: 'Document processing failed',
        errorDetails: error.message,
        errorStack: error.stack,
        failedAt: new Date().toISOString()
      });
    } catch (statusError) {
      console.error(`❌ Failed to update error status: ${statusError.message}`);
    }

    res.status(500).json({
      error: true,
      message: 'Document processing failed',
      details: error.message,
      documentId
    });
  }
}

/**
 * Direct database status update function
 */
async function updateDocumentStatusDirect(appId, documentId, status, additionalData = {}) {
  try {
    console.log(`📡 Updating status: Document ${documentId} → ${status}`);
    
    // Update directly in database
    await databaseService.updateDocumentStatus(documentId, appId, status, additionalData);
    
    console.log(`✅ Status updated successfully: ${status}`);
    return true;

  } catch (error) {
    console.error(`❌ Status update failed: ${error.message}`);
    
    // Optional: Fallback to HTTP API for backward compatibility
    console.log(`🔄 Attempting HTTP API fallback...`);
    try {
      await notifyUserServiceStatus(appId, documentId, status, additionalData.message || status, additionalData);
      console.log(`✅ HTTP API fallback successful`);
      return true;
    } catch (httpError) {
      console.error(`❌ HTTP API fallback also failed: ${httpError.message}`);
      throw error; // Throw original database error
    }
  }
}

/**
 * Enhanced error handling with detailed status updates
 */
async function handleProcessingError(appId, documentId, step, error) {
  const errorData = {
    message: `Processing failed at step: ${step}`,
    errorDetails: error.message,
    errorStack: error.stack,
    failedStep: step,
    failedAt: new Date().toISOString()
  };

  await updateDocumentStatusDirect(appId, documentId, 'error', errorData);
  
  console.error(`❌ Processing failed at ${step}:`, error.message);
  throw error;
}

/**
 * Progress tracking for long-running operations
 */
async function updateProgress(appId, documentId, status, progress, message) {
  await updateDocumentStatusDirect(appId, documentId, status, {
    message,
    progress: Math.round(progress),
    progressUpdatedAt: new Date().toISOString()
  });
}

module.exports = {
  processDocumentWithDirectStatusUpdates,
  updateDocumentStatusDirect,
  handleProcessingError,
  updateProgress
};

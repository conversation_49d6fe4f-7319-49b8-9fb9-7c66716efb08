# ChatAI Environment Variables Guide

## 📋 **Required Environment Variables for Integration**

This document lists all environment variables that need to be added to the target codebase for ChatAI functionality.

## 🔧 **User-Service Environment Variables**

### **Core ChatAI Configuration**
```env
# ChatAI SDK URL (for vector processing integration)
CHATAI_SDK_URL=http://localhost:3001

# Internal API Configuration (for service-to-service communication)
# Used by ChatAI-SDK-Clean to update document status in User-Service
INTERNAL_API_KEY=chatai-internal-2024

# ChatAI Origin (for CORS and key validation)
CHATAI_ORIGIN=http://localhost:3001
```

### **External API Keys**
```env
# LlamaIndex Cloud API Key (for document parsing)
# Get from: https://cloud.llamaindex.ai/
LLAMA_CLOUD_API_KEY=llx-your-llama-cloud-api-key-here

# OpenRouter API Key (for LLM responses)
# Get from: https://openrouter.ai/
OPENROUTER_API_KEY=sk-or-v1-your-openrouter-api-key-here
```

### **Optional API Keys**
```env
# OpenAI API Key (optional, for better embeddings)
# If not provided, system will use mock embeddings
OPENAI_API_KEY=sk-your-openai-api-key-here

# SambaNova API Key (optional, alternative embedding provider)
SAMBANOVA_API_KEY=your-sambanova-api-key-here
```

## 🚀 **ChatAI-SDK-Clean Service Environment Variables**

### **Create separate .env file for ChatAI-SDK-Clean service:**

```env
# Server Configuration
PORT=3001
NODE_ENV=development

# User Service Integration
USER_SERVICE_URL=http://localhost:3000

# ChatAI Origin for key validation
CHATAI_ORIGIN=http://localhost:3001

# Qdrant Vector Database Configuration
QDRANT_URL=http://localhost:6333
QDRANT_COLLECTION=chatai_documents

# Required API Keys
LLAMA_CLOUD_API_KEY=llx-your-llama-cloud-api-key-here
OPENROUTER_API_KEY=sk-or-v1-your-openrouter-api-key-here

# Optional API Keys (recommended for better performance)
OPENAI_API_KEY=sk-your-openai-api-key-here
SAMBANOVA_API_KEY=your-sambanova-api-key-here

# Cache Configuration
CACHE_TTL_MINUTES=15
MAX_SESSIONS=1000
CLEANUP_INTERVAL_MINUTES=5

# Rate Limiting
RATE_LIMIT_WINDOW_MINUTES=15
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info

# Internal API Configuration (must match User-Service)
INTERNAL_API_KEY=chatai-internal-2024

# Embedding Cache Configuration
EMBEDDING_CACHE_ENABLED=true
EMBEDDING_CACHE_MAX_SIZE=10000
EMBEDDING_CACHE_MAX_AGE=*********
```

## 🔐 **API Key Setup Instructions**

### **1. LlamaIndex Cloud API Key**
```bash
# 1. Visit https://cloud.llamaindex.ai/
# 2. Create account and login
# 3. Go to API Keys section
# 4. Generate new API key
# 5. Copy the key (starts with 'llx-')
```

### **2. OpenRouter API Key**
```bash
# 1. Visit https://openrouter.ai/
# 2. Create account and login
# 3. Go to Keys section
# 4. Generate new API key
# 5. Copy the key (starts with 'sk-or-v1-')
```

### **3. OpenAI API Key (Optional)**
```bash
# 1. Visit https://platform.openai.com/
# 2. Create account and login
# 3. Go to API Keys section
# 4. Generate new API key
# 5. Copy the key (starts with 'sk-')
```

### **4. SambaNova API Key (Optional)**
```bash
# 1. Visit SambaNova platform
# 2. Create account and get API access
# 3. Generate API key
# 4. Copy the key
```

## 🐳 **Docker Environment Variables**

### **If using Docker Compose:**
```yaml
# docker-compose.yml
version: '3.8'
services:
  user-service:
    environment:
      - CHATAI_SDK_URL=http://chatai-sdk:3001
      - INTERNAL_API_KEY=chatai-internal-2024
      - CHATAI_ORIGIN=http://chatai-sdk:3001
      - LLAMA_CLOUD_API_KEY=${LLAMA_CLOUD_API_KEY}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
  
  chatai-sdk:
    environment:
      - PORT=3001
      - USER_SERVICE_URL=http://user-service:3000
      - QDRANT_URL=http://qdrant:6333
      - LLAMA_CLOUD_API_KEY=${LLAMA_CLOUD_API_KEY}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - INTERNAL_API_KEY=chatai-internal-2024
  
  qdrant:
    image: qdrant/qdrant
    ports:
      - "6333:6333"
```

## 🔧 **Environment Variable Validation**

### **User-Service Validation Script:**
```javascript
// Add to your startup validation
const requiredEnvVars = [
  'CHATAI_SDK_URL',
  'INTERNAL_API_KEY',
  'LLAMA_CLOUD_API_KEY',
  'OPENROUTER_API_KEY'
];

const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
if (missingVars.length > 0) {
  console.error('❌ Missing required ChatAI environment variables:', missingVars);
  process.exit(1);
}
```

## 🌍 **Environment-Specific Configurations**

### **Development Environment**
```env
CHATAI_SDK_URL=http://localhost:3001
QDRANT_URL=http://localhost:6333
LOG_LEVEL=debug
NODE_ENV=development
```

### **Production Environment**
```env
CHATAI_SDK_URL=https://your-chatai-sdk-domain.com
QDRANT_URL=https://your-qdrant-instance.com
LOG_LEVEL=info
NODE_ENV=production
```

### **Staging Environment**
```env
CHATAI_SDK_URL=https://staging-chatai-sdk.your-domain.com
QDRANT_URL=https://staging-qdrant.your-domain.com
LOG_LEVEL=info
NODE_ENV=staging
```

## 📝 **Environment Variable Checklist**

### **Before Starting Integration:**
- [ ] Copy environment variables to target project's .env file
- [ ] Obtain all required API keys
- [ ] Update URLs for your environment (dev/staging/prod)
- [ ] Verify port availability (3001 for ChatAI-SDK-Clean)
- [ ] Test database connectivity
- [ ] Validate API key permissions

### **After Integration:**
- [ ] Test ChatAI endpoints respond correctly
- [ ] Verify document upload functionality
- [ ] Check vector database connectivity
- [ ] Validate external API integrations
- [ ] Monitor logs for any configuration issues

## 🚨 **Security Considerations**

### **API Key Security:**
```bash
# Never commit API keys to version control
echo "*.env" >> .gitignore
echo ".env.local" >> .gitignore
echo ".env.production" >> .gitignore

# Use environment-specific files
.env.development
.env.staging  
.env.production
```

### **Internal API Key:**
```bash
# Generate secure internal API key for production
openssl rand -hex 32
# Use this for INTERNAL_API_KEY in production
```

## 🔄 **Environment Variable Migration**

### **From Existing ChatAI Setup:**
```bash
# If migrating from existing ChatAI implementation
# Copy these variables from your current .env:
grep -E "(CHATAI|LLAMA|OPENROUTER|INTERNAL_API)" .env.old >> .env.new
```

### **Validation After Migration:**
```bash
# Test environment loading
node -e "
require('dotenv').config();
console.log('CHATAI_SDK_URL:', process.env.CHATAI_SDK_URL);
console.log('API Keys configured:', {
  llama: !!process.env.LLAMA_CLOUD_API_KEY,
  openrouter: !!process.env.OPENROUTER_API_KEY,
  internal: !!process.env.INTERNAL_API_KEY
});
"
```
